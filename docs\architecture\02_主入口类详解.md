# Freerouting 主入口类详解

## 📁 文件位置
`src/main/java/app/freerouting/Freerouting.java`

## 🎯 类的作用

`Freerouting` 类是整个应用程序的入口点，负责：
1. **应用程序启动和初始化**
2. **命令行参数解析**
3. **GUI模式和命令行模式的选择**
4. **API服务器的启动和管理**
5. **全局设置的初始化**

## 📋 类结构分析

### 主要成员变量

```java
public class Freerouting {
    // 应用程序官网URL
    public static final String WEB_URL = "https://www.freerouting.app";
    
    // 版本号字符串，包含构建日期
    public static final String VERSION_NUMBER_STRING = "v" + Constants.FREEROUTING_VERSION + 
                                                      " (build-date: " + Constants.FREEROUTING_BUILD_DATE + ")";
    
    // 全局设置对象，存储应用程序配置
    public static GlobalSettings globalSettings;
    
    // API服务器实例，用于提供REST API服务
    private static Server apiServer;
}
```

### 核心方法详解

#### 1. main() 方法 - 应用程序入口

```java
public static void main(String[] args) {
    // 1. 设置异常处理器
    Thread.setDefaultUncaughtExceptionHandler(new DefaultExceptionHandler());
    
    // 2. 初始化全局设置
    globalSettings = new GlobalSettings();
    
    // 3. 解析命令行参数
    parseCommandLineArguments(args);
    
    // 4. 根据模式启动应用
    if (globalSettings.webserver_mode) {
        startWebServerMode();
    } else {
        startGuiMode();
    }
}
```

**功能说明**：
- **异常处理**: 设置全局异常处理器，确保未捕获的异常能被正确处理
- **设置初始化**: 创建全局设置对象，加载默认配置
- **参数解析**: 解析命令行参数，确定运行模式和配置
- **模式选择**: 根据参数决定启动GUI模式还是Web服务器模式

#### 2. parseCommandLineArguments() - 命令行参数解析

```java
private static void parseCommandLineArguments(String[] args) {
    for (int i = 0; i < args.length; i++) {
        String arg = args[i];
        
        switch (arg) {
            case "-de":  // 德语界面
                globalSettings.current_locale = Locale.GERMAN;
                break;
                
            case "-en":  // 英语界面
                globalSettings.current_locale = Locale.ENGLISH;
                break;
                
            case "-web":  // Web服务器模式
                globalSettings.webserver_mode = true;
                break;
                
            case "-port":  // API服务器端口
                if (i + 1 < args.length) {
                    globalSettings.api_server_port = Integer.parseInt(args[++i]);
                }
                break;
                
            case "-input":  // 输入文件
                if (i + 1 < args.length) {
                    globalSettings.design_input_filename = args[++i];
                }
                break;
                
            case "-output":  // 输出文件
                if (i + 1 < args.length) {
                    globalSettings.design_output_filename = args[++i];
                }
                break;
                
            case "-help":  // 显示帮助
                printUsage();
                System.exit(0);
                break;
        }
    }
}
```

**支持的命令行参数**：
- `-de` / `-en`: 设置界面语言
- `-web`: 启用Web服务器模式
- `-port <端口号>`: 设置API服务器端口
- `-input <文件路径>`: 指定输入PCB文件
- `-output <文件路径>`: 指定输出文件路径
- `-help`: 显示帮助信息

#### 3. startGuiMode() - GUI模式启动

```java
private static void startGuiMode() {
    // 1. 设置系统外观
    try {
        UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
    } catch (Exception e) {
        FRLogger.warn("无法设置系统外观", e);
    }
    
    // 2. 初始化文本管理器
    TextManager.initialize();
    
    // 3. 启动API服务器（如果启用）
    if (globalSettings.api_server_enabled) {
        startApiServer();
    }
    
    // 4. 创建并显示欢迎窗口
    SwingUtilities.invokeLater(() -> {
        WindowWelcome welcomeWindow = new WindowWelcome();
        welcomeWindow.setVisible(true);
    });
}
```

**GUI模式特点**：
- **系统外观**: 使用操作系统原生的界面风格
- **多语言支持**: 根据设置加载相应的语言资源
- **API服务**: 可选择性启动API服务器
- **欢迎界面**: 显示用户友好的启动界面

#### 4. startWebServerMode() - Web服务器模式

```java
private static void startWebServerMode() {
    FRLogger.info("启动Web服务器模式...");
    
    // 1. 初始化文本管理器
    TextManager.initialize();
    
    // 2. 启动API服务器
    startApiServer();
    
    // 3. 处理命令行指定的文件
    if (globalSettings.design_input_filename != null) {
        processInputFile();
    }
    
    // 4. 保持服务器运行
    try {
        Thread.currentThread().join();
    } catch (InterruptedException e) {
        FRLogger.info("Web服务器模式被中断");
    }
}
```

**Web服务器模式特点**：
- **无GUI**: 纯命令行模式，适合服务器部署
- **REST API**: 提供完整的API接口
- **批处理**: 支持命令行指定文件的自动处理
- **持续运行**: 服务器保持运行状态等待请求

#### 5. startApiServer() - API服务器启动

```java
private static void startApiServer() {
    try {
        // 1. 创建Jetty服务器
        apiServer = new Server();
        
        // 2. 配置连接器
        ServerConnector connector = new ServerConnector(apiServer);
        connector.setPort(globalSettings.api_server_port);
        apiServer.addConnector(connector);
        
        // 3. 配置Servlet上下文
        ServletContextHandler context = new ServletContextHandler();
        context.setContextPath("/");
        
        // 4. 配置Jersey Servlet
        ServletHolder jerseyServlet = context.addServlet(ServletContainer.class, "/api/*");
        jerseyServlet.setInitParameter("jersey.config.server.provider.packages", 
                                      "app.freerouting.api");
        
        // 5. 添加上下文监听器
        context.addEventListener(new AppContextListener());
        
        // 6. 启动服务器
        apiServer.setHandler(context);
        apiServer.start();
        
        FRLogger.info("API服务器已启动，端口: " + globalSettings.api_server_port);
        
    } catch (Exception e) {
        FRLogger.error("API服务器启动失败", e);
    }
}
```

**API服务器特性**：
- **Jetty服务器**: 轻量级、高性能的Web服务器
- **Jersey框架**: JAX-RS实现，提供REST API
- **可配置端口**: 支持自定义端口号
- **上下文监听**: 支持应用生命周期管理

## 🔄 应用程序启动流程

```mermaid
graph TD
    A[main方法启动] --> B[设置异常处理器]
    B --> C[初始化全局设置]
    C --> D[解析命令行参数]
    D --> E{检查运行模式}
    E -->|GUI模式| F[设置系统外观]
    E -->|Web模式| G[启动API服务器]
    F --> H[初始化文本管理器]
    G --> I[处理输入文件]
    H --> J{API服务器启用?}
    J -->|是| K[启动API服务器]
    J -->|否| L[显示欢迎窗口]
    K --> L
    I --> M[保持服务器运行]
```

## ⚙️ 配置管理

### GlobalSettings 全局设置

```java
public class GlobalSettings {
    // 界面语言设置
    public Locale current_locale = Locale.ENGLISH;
    
    // 运行模式
    public boolean webserver_mode = false;
    
    // API服务器配置
    public boolean api_server_enabled = true;
    public int api_server_port = 8080;
    
    // 文件路径
    public String design_input_filename = null;
    public String design_output_filename = null;
    
    // 其他设置...
}
```

### 设置的作用域

- **全局访问**: 通过 `Freerouting.globalSettings` 静态访问
- **线程安全**: 主要在启动阶段设置，运行时只读
- **持久化**: 部分设置可保存到配置文件

## 🚀 使用示例

### GUI模式启动
```bash
java -jar freerouting.jar
java -jar freerouting.jar -de  # 德语界面
```

### Web服务器模式
```bash
java -jar freerouting.jar -web -port 8080
```

### 批处理模式
```bash
java -jar freerouting.jar -input design.dsn -output result.dsn
```

### 组合使用
```bash
java -jar freerouting.jar -web -port 9090 -input design.dsn
```

## 🔧 扩展点

### 1. 新增命令行参数
在 `parseCommandLineArguments()` 方法中添加新的参数处理逻辑。

### 2. 自定义启动模式
可以添加新的启动模式，如批处理模式、调试模式等。

### 3. 配置文件支持
可以扩展为支持从配置文件加载设置。

### 4. 插件系统
可以在启动阶段加载和初始化插件。

这个主入口类设计简洁而功能完整，为freerouting提供了灵活的启动方式和良好的扩展性。
