package app.freerouting.autoroute;

import app.freerouting.board.SearchTreeObject;
import app.freerouting.boardgraphics.GraphicsContext;
import app.freerouting.geometry.planar.Point;
import app.freerouting.geometry.planar.TileShape;

import java.awt.*;
import java.util.Collection;
import java.util.Iterator;

/**
 * Layer change expansion object in the maze search algorithm.
 */
public class ExpansionDrill implements ExpandableObject
{

  /**
   * The location, where the drill is checked.
   */
  public final Point location;
  /**
   * The first layer of the drill
   */
  public final int first_layer;
  /**
   * The last layer of the drill
   */
  public final int last_layer;
  /**
   * Array of dimension last_layer - first_layer + 1.
   */
  public final CompleteExpansionRoom[] room_arr;
  private final MazeSearchElement[] maze_search_info_arr;
  /**
   * The shape of the drill.
   */
  private final TileShape shape;

  /**
   * Creates a new instance of Drill
   */
  public ExpansionDrill(TileShape p_shape, Point p_location, int p_first_layer, int p_last_layer)
  {
    shape = p_shape;
    location = p_location;
    first_layer = p_first_layer;
    last_layer = p_last_layer;
    int layer_count = p_last_layer - p_first_layer + 1;
    room_arr = new CompleteExpansionRoom[layer_count];
    maze_search_info_arr = new MazeSearchElement[layer_count];
    for (int i = 0; i < maze_search_info_arr.length; ++i)
    {
      maze_search_info_arr[i] = new MazeSearchElement();
    }
  }

  /**
   * Looks for the expansion room of this drill on each layer. Creates a
   * CompleteFreeSpaceExpansionRoom, if no expansion room is found. Returns false, if that was not
   * possible because of an obstacle at this.location on some layer in the compensated search tree.
   */
  public boolean calculate_expansion_rooms(AutorouteEngine p_autoroute_engine)
  {
    TileShape search_shape = TileShape.get_instance(location);
    Collection<SearchTreeObject> overlaps = p_autoroute_engine.autoroute_search_tree.overlapping_objects(search_shape, -1);
    for (int i = this.first_layer; i <= this.last_layer; ++i)
    {
      CompleteExpansionRoom found_room = null;
      Iterator<SearchTreeObject> it = overlaps.iterator();
      while (it.hasNext())
      {
        SearchTreeObject curr_ob = it.next();
        if (!(curr_ob instanceof CompleteExpansionRoom curr_room))
        {
          it.remove();
          continue;
        }
        if (curr_room.get_layer() == i)
        {
          found_room = curr_room;
          it.remove();
          break;
        }
      }
      if (found_room == null)
      {
        // create a new expansion room on this layer
        IncompleteFreeSpaceExpansionRoom new_incomplete_room = new IncompleteFreeSpaceExpansionRoom(null, i, search_shape);
        Collection<CompleteFreeSpaceExpansionRoom> new_rooms = p_autoroute_engine.complete_expansion_room(new_incomplete_room);
        if (new_rooms.size() != 1)
        {
          // the size may be 0 because of an obstacle in the compensated tree at this.location
          return false;
        }
        Iterator<CompleteFreeSpaceExpansionRoom> it2 = new_rooms.iterator();
        if (it2.hasNext())
        {
          found_room = it2.next();
        }
      }
      this.room_arr[i - first_layer] = found_room;
    }
    return true;
  }

  @Override
  public TileShape get_shape()
  {
    return this.shape;
  }

  @Override
  public int get_dimension()
  {
    return 2;
  }

  @Override
  public CompleteExpansionRoom other_room(CompleteExpansionRoom p_room)
  {
    return null;
  }

  @Override
  public int maze_search_element_count()
  {
    return this.maze_search_info_arr.length;
  }

  @Override
  public MazeSearchElement get_maze_search_element(int p_no)
  {
    return this.maze_search_info_arr[p_no];
  }

  @Override
  public void reset()
  {
    for (MazeSearchElement curr_info : maze_search_info_arr)
    {
      curr_info.reset();
    }
  }

  /*
   * Test draw of the shape of this drill.
   * 绘制过孔形状 - 增强可视化效果，清晰边界
   */
  public void draw(Graphics p_graphics, GraphicsContext p_graphics_context, double p_intensity)
  {
    // 使用明亮的蓝色来表示过孔，提高可见性
    Color fill_color = new Color(0, 150, 255, 120); // 明亮蓝色，半透明
    Color border_color = new Color(0, 80, 180, 255); // 深蓝色边框，完全不透明
    Color text_color = new Color(255, 255, 255, 255); // 白色文字
    Color text_bg_color = new Color(0, 100, 200, 200); // 深蓝色文字背景

    // 填充过孔区域
    p_graphics_context.fill_area(this.shape, p_graphics, fill_color,
                                Math.max(0.6, p_intensity));

    // 绘制多层边界以增强可见性
    // 外层边界 - 更粗更明显
    p_graphics_context.draw_boundary(this.shape, 3.0, border_color, p_graphics,
                                   Math.max(0.9, p_intensity));

    // 内层边界 - 细线增强对比
    Color inner_border = new Color(0, 120, 220, 180);
    p_graphics_context.draw_boundary(this.shape, 1.0, inner_border, p_graphics,
                                   Math.max(0.7, p_intensity));

    // 绘制过孔标识
    draw_drill_label(p_graphics, p_graphics_context, text_color, text_bg_color, p_intensity);
  }

  /**
   * 绘制过孔标识 - 增强版本，带背景和更好的可见性
   */
  private void draw_drill_label(Graphics p_graphics, GraphicsContext p_graphics_context,
                               Color p_text_color, Color p_bg_color, double p_intensity)
  {
    try
    {
      // 获取过孔的中心点
      app.freerouting.geometry.planar.IntBox bounding_box = this.shape.bounding_box();
      if (bounding_box == null) return;

      // 检查过孔大小，只在足够大的过孔中显示标识
      int drill_width = bounding_box.ur.x - bounding_box.ll.x;
      int drill_height = bounding_box.ur.y - bounding_box.ll.y;
      if (drill_width < 600 || drill_height < 600) return; // 小于0.6mm的过孔不显示标识

      int center_x = (bounding_box.ll.x + bounding_box.ur.x) / 2;
      int center_y = (bounding_box.ll.y + bounding_box.ur.y) / 2;

      // 转换为屏幕坐标
      java.awt.geom.Point2D screen_point = p_graphics_context.coordinate_transform.board_to_screen(
          new app.freerouting.geometry.planar.FloatPoint(center_x, center_y));

      // 设置字体和颜色
      java.awt.Graphics2D g2d = (java.awt.Graphics2D) p_graphics;
      g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING,
                          java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
      g2d.setFont(new java.awt.Font("Arial", java.awt.Font.BOLD, 10));

      // 绘制过孔标识文字
      String drill_text = "V";
      java.awt.FontMetrics fm = g2d.getFontMetrics();
      int text_width = fm.stringWidth(drill_text);
      int text_height = fm.getHeight();

      int text_x = (int)(screen_point.getX() - text_width/2);
      int text_y = (int)(screen_point.getY() + text_height/4);

      // 绘制文字背景（圆形）
      int radius = Math.max(text_width, text_height) / 2 + 2;
      int bg_x = (int)(screen_point.getX() - radius);
      int bg_y = (int)(screen_point.getY() - radius);

      g2d.setColor(p_bg_color);
      g2d.fillOval(bg_x, bg_y, radius * 2, radius * 2);

      // 绘制文字边框
      g2d.setColor(new Color(0, 60, 120, 255));
      g2d.setStroke(new java.awt.BasicStroke(1.0f));
      g2d.drawOval(bg_x, bg_y, radius * 2, radius * 2);

      // 绘制文字
      g2d.setColor(p_text_color);
      g2d.drawString(drill_text, text_x, text_y);
    }
    catch (Exception e)
    {
      // 忽略绘制错误
    }
  }
}