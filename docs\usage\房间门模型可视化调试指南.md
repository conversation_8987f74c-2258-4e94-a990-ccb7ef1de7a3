# Freerouting 房间门模型可视化调试指南

## 🎯 概述

本指南将帮助你启用和使用freerouting的房间门模型可视化调试功能。通过这个功能，你可以直观地观察自动布线算法的内部工作原理，包括房间的生成、门的连接以及迷宫搜索的过程。

## 🔧 已实施的修改

### 1. 启用钻孔页面绘制
**文件**: `src/main/java/app/freerouting/autoroute/DrillPage.java`
- 移除了绘制禁用条件 `if (true) return;`
- 添加了页面边界绘制（紫色半透明）
- 启用了钻孔点的可视化

### 2. 启用AutorouteEngine绘制
**文件**: `src/main/java/app/freerouting/autoroute/AutorouteEngine.java`
- 取消注释钻孔页面绘制调用
- 添加了详细的中文注释
- 增强了绘制功能的说明

### 3. 添加菜单项
**文件**: `src/main/java/app/freerouting/gui/BoardMenuDisplay.java`
- 在"显示"菜单中添加了"房间门模型调试"选项
- 提供了用户友好的访问入口

### 4. 添加调试模式方法
**文件**: `src/main/java/app/freerouting/interactive/GuiBoardManager.java`
- 新增 `enter_room_door_debug_mode()` 方法
- 自动创建ExpandTestState来启用可视化
- 提供状态消息和用户指导

## 🚀 使用步骤

### 步骤1: 编译项目

```bash
cd D:\freerouting\freerouting-master
.\gradlew.bat build -x test
```

**预期结果**: 编译成功，可能有一些警告但不影响功能。

### 步骤2: 启动freerouting

```bash
java -jar build\libs\freerouting-executable.jar
```

### 步骤3: 加载PCB文件

1. 点击 **"File"** → **"Open..."**
2. 选择一个PCB设计文件（推荐使用 `.dsn` 格式）
3. 等待文件加载完成

**推荐测试文件**:
- `test_files\minimal_connection.dsn` - 最简单的两引脚连接（推荐新手）
- `test_files\simple_two_pins.dsn` - 简单的多引脚芯片连接
- `tests\Issue006-LPC18XX_43XX_SCH.dsn`（如果存在）
- 任何包含未连接网络的PCB文件

### 步骤4: 启用房间门模型调试

1. 点击菜单 **"显示"** → **"房间门模型调试"**
2. 系统会自动进入调试模式
3. 状态栏显示: "房间门模型调试模式已启用 - 按'n'展开下一步，按ESC退出"

### 步骤5: 观察房间门模型

#### 自动模式（推荐新手）
- 系统会自动在电路板中心开始扩展算法
- 观察房间的逐步生成过程

#### 手动控制模式
- **按 'n' 键**: 展开下一个房间
- **按 ESC 键**: 退出调试模式
- **鼠标点击**: 在指定位置开始新的扩展

## 🎨 可视化元素说明

### 房间类型

| 颜色 | 元素 | 含义 | 透明度 |
|------|------|------|--------|
| 🟢 **明亮绿色** | 自由空间房间 | 可以进行布线的区域 | 60% 不透明 |
| 🔴 **明亮红色** | 障碍物房间 | 被PCB对象占用的区域 | 50% 不透明 |
| 🟣 **明亮紫色** | 钻孔页面边界 | 过孔候选区域的网格 | 40% 不透明 |

### 连接元素

| 颜色 | 元素 | 含义 |
|------|------|------|
| 🔵 **蓝色线条** | 1维门 | 线段形式的房间连接 |
| 🟡 **黄色区域** | 2维门 | 区域形式的房间连接 |
| ⚫ **黑色点** | 钻孔点 | 过孔的候选位置 |

### 动态过程

- **房间扩展**: 观察房间如何从起始点逐步扩展
- **门的生成**: 看到房间之间如何建立连接
- **搜索过程**: 理解迷宫搜索算法的工作方式

## 🔍 调试技巧

### 1. 选择合适的测试位置
- 选择有多个网络交汇的区域
- 避免选择空旷的区域（房间扩展不明显）
- 选择有障碍物的区域（能看到障碍物房间）

### 2. 观察要点
- **房间形状**: 注意房间的几何形状如何适应PCB布局
- **门的位置**: 观察门在哪里生成，为什么在那里生成
- **扩展顺序**: 理解算法的扩展优先级

### 3. 性能考虑
- 大型PCB可能需要较长时间生成房间
- 可以通过按ESC随时中断过程
- 建议从小型PCB开始学习

## 🐛 故障排除

### 问题1: 菜单项不存在
**原因**: 编译不完整或文件未正确修改
**解决方案**:
```bash
.\gradlew.bat clean build -x test
```

### 问题2: 点击菜单项无反应
**原因**: 没有加载PCB文件
**解决方案**: 先加载一个有效的PCB文件

### 问题3: 没有看到可视化内容
**可能原因**:
- PCB文件没有未连接的网络
- 选择的位置没有可扩展的空间
- 透明度设置过低

**解决方案**:
- 尝试不同的PCB文件
- 按'n'键手动推进算法
- 检查是否有错误消息

### 问题4: 程序崩溃或异常
**解决方案**:
- 按ESC退出调试模式
- 重新加载PCB文件
- 检查控制台错误信息

## 📊 高级用法

### 1. 结合自动布线使用
```
1. 启用房间门调试模式
2. 选择一个引脚
3. 右键 → "Autoroute"
4. 观察实际布线过程中的房间门生成
```

### 2. 分析算法性能
- 观察房间生成的数量
- 分析门的连接效率
- 评估算法的空间利用率

### 3. 算法参数调优
- 修改AutorouteEngine中的参数
- 观察参数变化对房间门模型的影响
- 优化特定PCB类型的布线效果

## 🎓 学习建议

### 初学者路径
1. **理解基本概念**: 先阅读房间门模型的理论
2. **简单观察**: 使用小型PCB观察基本的房间生成
3. **手动控制**: 学会使用'n'键控制扩展过程
4. **分析结果**: 理解不同颜色区域的含义

### 进阶用户路径
1. **算法分析**: 深入理解扩展算法的逻辑
2. **性能优化**: 分析算法在不同PCB上的表现
3. **参数调优**: 修改算法参数观察效果变化
4. **功能扩展**: 添加新的可视化元素

### 开发者路径
1. **代码理解**: 深入研究相关源代码
2. **功能增强**: 添加新的调试功能
3. **算法改进**: 基于可视化结果改进算法
4. **工具开发**: 开发专门的分析工具

## 📝 注意事项

### 性能影响
- 可视化功能会增加内存使用
- 大型PCB的房间生成可能较慢
- 建议在调试完成后退出可视化模式

### 数据准确性
- 可视化显示的是算法的内部状态
- 不同的PCB文件可能产生不同的房间结构
- 房间门模型是算法的简化表示

### 兼容性
- 功能在所有支持的操作系统上可用
- 需要Java 17+环境
- 建议使用较新的显卡驱动以获得最佳性能

## 🔗 相关资源

- [项目总体架构文档](../architecture/01_项目总体架构.md)
- [自动布线模块详解](../architecture/03_自动布线模块详解.md)
- [绘制函数调用关系分析](../analysis/绘制函数调用关系分析.md)

---

**提示**: 这个可视化功能是理解freerouting自动布线算法的强大工具。通过观察房间门模型的生成过程，你可以深入理解现代PCB自动布线算法的工作原理。
