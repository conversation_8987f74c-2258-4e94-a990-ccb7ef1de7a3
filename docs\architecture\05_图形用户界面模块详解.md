# Freerouting 图形用户界面模块详解

## 📁 模块位置
`src/main/java/app/freerouting/gui/`

## 🎯 模块概述

图形用户界面模块是freerouting的用户交互层，基于Java Swing框架构建。该模块提供了完整的PCB设计和布线的可视化界面，包括主窗口、工具栏、菜单系统、对话框等组件，为用户提供直观友好的操作体验。

## 🏗️ GUI架构设计

### 主要组件层次结构

```mermaid
graph TD
    A[BoardFrame 主窗口] --> B[BoardMenuBar 菜单栏]
    A --> C[BoardToolbar 工具栏]
    A --> D[BoardPanel 绘图面板]
    A --> E[BoardPanelStatus 状态栏]
    
    B --> F[BoardMenuFile 文件菜单]
    B --> G[BoardMenuDisplay 显示菜单]
    B --> H[BoardMenuParameter 参数菜单]
    B --> I[BoardMenuRules 规则菜单]
    
    D --> J[GuiBoardManager 交互管理器]
    J --> K[InteractiveState 交互状态]
    
    A --> L[WindowXXX 各种对话框]
    L --> M[WindowWelcome 欢迎窗口]
    L --> N[WindowObjectInfo 对象信息窗口]
    L --> O[WindowAutorouteParameter 自动布线参数窗口]
```

## 📋 主要类详解

### 1. BoardFrame.java - 主窗口框架

```java
/**
 * 主窗口框架类 - freerouting的主界面容器
 * 包含菜单栏、工具栏、绘图区域和状态栏
 */
public class BoardFrame extends WindowBase {
    // 子窗口数量常量
    static final int SUBWINDOW_COUNT = 24;
    
    // 布线任务对象
    public final RoutingJob routingJob;
    
    // 主要UI组件
    public BoardPanel board_panel;              // 绘图面板
    public BoardMenuBar menubar;                // 菜单栏
    public BoardToolbar toolbar;                // 工具栏
    public BoardPanelStatus message_panel;      // 状态栏
    
    // 各种子窗口
    public WindowObjectInfo object_info_window;
    public WindowAutorouteParameter autoroute_parameter_window;
    public WindowRouteParameter route_parameter_window;
    public WindowSelectParameter select_parameter_window;
    public WindowMoveParameter move_parameter_window;
    public WindowClearanceMatrix clearance_matrix_window;
    // ... 其他窗口
    
    // 设计文件相关
    private File design_file = null;
    private boolean is_intermediate_stage_file = false;
    
    /**
     * 构造函数 - 创建主窗口
     */
    public BoardFrame(RoutingJob p_routing_job) {
        super(1200, 800); // 设置默认窗口大小
        
        this.routingJob = p_routing_job;
        
        // 初始化UI组件
        initialize_components();
        
        // 设置窗口属性
        setup_window_properties();
        
        // 加载GUI默认设置
        load_gui_defaults();
        
        // 设置事件监听器
        setup_event_listeners();
    }
    
    /**
     * 初始化UI组件
     */
    private void initialize_components() {
        // 创建菜单栏
        this.menubar = new BoardMenuBar(this);
        this.setJMenuBar(menubar);
        
        // 创建工具栏
        this.toolbar = new BoardToolbar(this);
        
        // 创建绘图面板
        this.board_panel = new BoardPanel(this);
        
        // 创建状态栏
        this.message_panel = new BoardPanelStatus();
        
        // 布局组件
        layout_components();
    }
    
    /**
     * 布局UI组件
     */
    private void layout_components() {
        this.setLayout(new BorderLayout());
        
        // 添加工具栏到北部
        this.add(toolbar, BorderLayout.NORTH);
        
        // 添加绘图面板到中央
        this.add(board_panel, BorderLayout.CENTER);
        
        // 添加状态栏到南部
        this.add(message_panel, BorderLayout.SOUTH);
    }
    
    /**
     * 打开设计文件
     */
    public boolean load_design_file(File p_design_file) {
        try {
            FRLogger.info("正在加载设计文件: " + p_design_file.getName());
            
            // 检查文件格式
            String file_extension = get_file_extension(p_design_file);
            
            if (file_extension.equals("dsn")) {
                // 加载Specctra DSN文件
                return load_dsn_file(p_design_file);
            } else if (file_extension.equals("bin")) {
                // 加载二进制文件
                return load_binary_file(p_design_file);
            } else {
                show_error_message("不支持的文件格式: " + file_extension);
                return false;
            }
            
        } catch (Exception e) {
            FRLogger.error("加载设计文件失败", e);
            show_error_message("加载文件失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 加载DSN文件
     */
    private boolean load_dsn_file(File p_file) {
        try {
            // 使用DSN文件读取器
            InputStream input_stream = new FileInputStream(p_file);
            DsnFile.ReadResult read_result = DsnFile.read(input_stream, 
                                                         routingJob.boardFileDetails,
                                                         this);
            
            if (read_result.board == null) {
                show_error_message("DSN文件读取失败");
                return false;
            }
            
            // 设置电路板
            create_board_handling(read_result.board);
            
            // 更新窗口标题
            this.design_file = p_file;
            update_window_title();
            
            // 刷新显示
            refresh_windows();
            
            FRLogger.info("DSN文件加载成功");
            return true;
            
        } catch (Exception e) {
            FRLogger.error("DSN文件加载失败", e);
            return false;
        }
    }
    
    /**
     * 创建电路板处理器
     */
    private void create_board_handling(RoutingBoard p_board) {
        // 创建GUI电路板管理器
        GuiBoardManager board_manager = new GuiBoardManager(this, p_board);
        
        // 设置到绘图面板
        board_panel.set_board_handling(board_manager);
        
        // 初始化交互状态
        board_manager.set_interactive_state(InteractiveState.MENU_STATE);
        
        // 更新工具栏状态
        toolbar.update_toolbar_state();
        
        // 启用相关菜单项
        menubar.enable_board_menus();
    }
}
```

**主要功能**：
- **窗口管理**: 管理主窗口和所有子窗口
- **文件操作**: 处理设计文件的加载和保存
- **UI协调**: 协调各个UI组件的交互
- **状态管理**: 管理应用程序的各种状态

### 2. BoardPanel.java - 绘图面板

```java
/**
 * 绘图面板类 - PCB的可视化显示区域
 * 负责渲染PCB内容和处理用户交互
 */
public class BoardPanel extends JPanel {
    // 电路板管理器
    private GuiBoardManager board_handling;
    
    // 图形上下文
    private GraphicsContext graphics_context;
    
    // 缩放和平移参数
    private double zoom_factor = 1.0;
    private Point2D viewport_center = new Point2D.Double(0, 0);
    
    // 鼠标交互状态
    private Point last_mouse_position;
    private boolean is_dragging = false;
    
    /**
     * 构造函数
     */
    public BoardPanel(BoardFrame p_board_frame) {
        this.board_frame = p_board_frame;
        
        // 设置面板属性
        setBackground(Color.BLACK);
        setDoubleBuffered(true);
        
        // 初始化图形上下文
        this.graphics_context = new GraphicsContext();
        
        // 设置鼠标监听器
        setup_mouse_listeners();
        
        // 设置键盘监听器
        setup_keyboard_listeners();
        
        // 设置焦点
        setFocusable(true);
    }
    
    /**
     * 绘制方法 - 渲染PCB内容
     */
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        
        if (board_handling == null) {
            // 显示欢迎信息
            draw_welcome_message(g);
            return;
        }
        
        // 转换为Graphics2D
        Graphics2D g2d = (Graphics2D) g.create();
        
        try {
            // 设置渲染提示
            setup_rendering_hints(g2d);
            
            // 应用视口变换
            apply_viewport_transform(g2d);
            
            // 绘制电路板内容
            draw_board_content(g2d);
            
            // 绘制用户界面元素
            draw_ui_elements(g2d);
            
        } finally {
            g2d.dispose();
        }
    }
    
    /**
     * 绘制电路板内容
     */
    private void draw_board_content(Graphics2D p_graphics) {
        RoutingBoard board = board_handling.get_routing_board();
        if (board == null) return;
        
        // 设置图形上下文
        graphics_context.set_graphics(p_graphics);
        graphics_context.set_zoom_factor(zoom_factor);
        
        // 绘制电路板轮廓
        draw_board_outline(p_graphics);
        
        // 绘制PCB对象
        draw_board_items(p_graphics);
        
        // 绘制网络连接（飞线）
        draw_ratsnest(p_graphics);
        
        // 绘制选中对象的高亮
        draw_selection_highlights(p_graphics);
        
        // 绘制交互反馈
        draw_interactive_feedback(p_graphics);
    }
    
    /**
     * 绘制PCB对象
     */
    private void draw_board_items(Graphics2D p_graphics) {
        RoutingBoard board = board_handling.get_routing_board();
        
        // 获取当前视口区域
        IntBox visible_area = get_visible_area();
        
        // 查找可见区域内的对象
        Collection<Item> visible_items = board.overlapping_objects(visible_area);
        
        // 按层和类型排序绘制
        List<Item> sorted_items = sort_items_for_drawing(visible_items);
        
        for (Item item : sorted_items) {
            // 检查对象是否可见
            if (is_item_visible(item)) {
                // 获取绘制颜色
                Color[] colors = get_item_colors(item);
                
                // 绘制对象
                item.draw(p_graphics, graphics_context, colors, 1.0);
            }
        }
    }
    
    /**
     * 处理鼠标点击事件
     */
    private void handle_mouse_click(MouseEvent e) {
        if (board_handling == null) return;
        
        // 转换鼠标坐标到电路板坐标
        Point board_point = screen_to_board_coordinates(e.getPoint());
        
        // 根据当前交互状态处理点击
        InteractiveState current_state = board_handling.get_interactive_state();
        
        if (e.getButton() == MouseEvent.BUTTON1) {
            // 左键点击
            current_state.left_button_clicked(board_point);
        } else if (e.getButton() == MouseEvent.BUTTON3) {
            // 右键点击 - 显示上下文菜单
            show_context_menu(e.getPoint(), board_point);
        }
        
        // 刷新显示
        repaint();
    }
    
    /**
     * 处理鼠标拖拽事件
     */
    private void handle_mouse_drag(MouseEvent e) {
        if (board_handling == null) return;
        
        Point current_position = e.getPoint();
        
        if (last_mouse_position != null) {
            // 计算拖拽距离
            int dx = current_position.x - last_mouse_position.x;
            int dy = current_position.y - last_mouse_position.y;
            
            if (SwingUtilities.isMiddleMouseButton(e)) {
                // 中键拖拽 - 平移视图
                pan_viewport(dx, dy);
            } else {
                // 其他拖拽 - 传递给交互状态处理
                Point board_point = screen_to_board_coordinates(current_position);
                board_handling.get_interactive_state().mouse_dragged(board_point);
            }
        }
        
        last_mouse_position = current_position;
        repaint();
    }
    
    /**
     * 缩放视图
     */
    public void zoom_to_factor(double p_zoom_factor) {
        this.zoom_factor = Math.max(0.01, Math.min(100.0, p_zoom_factor));
        
        // 更新图形上下文
        graphics_context.set_zoom_factor(zoom_factor);
        
        // 刷新显示
        repaint();
        
        // 更新状态栏
        board_frame.message_panel.set_zoom_factor(zoom_factor);
    }
    
    /**
     * 适应窗口大小
     */
    public void zoom_to_fit() {
        if (board_handling == null) return;
        
        RoutingBoard board = board_handling.get_routing_board();
        IntBox board_bounds = board.bounding_box;
        
        // 计算合适的缩放因子
        Dimension panel_size = getSize();
        double zoom_x = (double) panel_size.width / board_bounds.width();
        double zoom_y = (double) panel_size.height / board_bounds.height();
        
        double new_zoom = Math.min(zoom_x, zoom_y) * 0.9; // 留一些边距
        
        // 设置视口中心
        viewport_center = new Point2D.Double(
            board_bounds.centre().x,
            board_bounds.centre().y
        );
        
        // 应用缩放
        zoom_to_factor(new_zoom);
    }
}
```

### 3. BoardMenuBar.java - 菜单栏

```java
/**
 * 菜单栏类 - 主窗口的菜单系统
 * 提供文件、编辑、显示、参数等各种菜单功能
 */
public class BoardMenuBar extends JMenuBar {
    private final BoardFrame board_frame;
    
    // 各个菜单
    private JMenu file_menu;
    private JMenu display_menu;
    private JMenu parameter_menu;
    private JMenu rules_menu;
    private JMenu info_menu;
    private JMenu help_menu;
    
    /**
     * 构造函数
     */
    public BoardMenuBar(BoardFrame p_board_frame) {
        this.board_frame = p_board_frame;
        
        // 创建各个菜单
        create_file_menu();
        create_display_menu();
        create_parameter_menu();
        create_rules_menu();
        create_info_menu();
        create_help_menu();
    }
    
    /**
     * 创建文件菜单
     */
    private void create_file_menu() {
        file_menu = BoardMenuFile.create_file_menu(board_frame);
        this.add(file_menu);
    }
    
    /**
     * 创建显示菜单
     */
    private void create_display_menu() {
        display_menu = BoardMenuDisplay.create_display_menu(board_frame);
        this.add(display_menu);
    }
    
    /**
     * 启用电路板相关菜单
     */
    public void enable_board_menus() {
        // 启用需要电路板才能使用的菜单项
        enable_menu_items(display_menu, true);
        enable_menu_items(parameter_menu, true);
        enable_menu_items(rules_menu, true);
    }
    
    /**
     * 禁用电路板相关菜单
     */
    public void disable_board_menus() {
        enable_menu_items(display_menu, false);
        enable_menu_items(parameter_menu, false);
        enable_menu_items(rules_menu, false);
    }
}
```

### 4. WindowBase.java - 窗口基类

```java
/**
 * 窗口基类 - 所有窗口的基础类
 * 提供通用的窗口功能和属性
 */
public class WindowBase extends JFrame {
    // 窗口的默认大小
    protected static final int DEFAULT_WIDTH = 800;
    protected static final int DEFAULT_HEIGHT = 600;
    
    /**
     * 构造函数
     */
    public WindowBase(int p_width, int p_height) {
        // 设置窗口大小
        setSize(p_width, p_height);
        
        // 设置窗口属性
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        
        // 居中显示
        setLocationRelativeTo(null);
        
        // 设置图标
        set_window_icon();
        
        // 设置外观
        setup_look_and_feel();
    }
    
    /**
     * 设置窗口图标
     */
    private void set_window_icon() {
        try {
            // 加载应用程序图标
            ImageIcon icon = new ImageIcon(getClass().getResource("/icon.png"));
            setIconImage(icon.getImage());
        } catch (Exception e) {
            // 忽略图标加载失败
        }
    }
    
    /**
     * 显示错误消息
     */
    protected void show_error_message(String p_message) {
        JOptionPane.showMessageDialog(this, p_message, "错误", 
                                     JOptionPane.ERROR_MESSAGE);
    }
    
    /**
     * 显示信息消息
     */
    protected void show_info_message(String p_message) {
        JOptionPane.showMessageDialog(this, p_message, "信息", 
                                     JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * 显示确认对话框
     */
    protected boolean show_confirm_dialog(String p_message) {
        int result = JOptionPane.showConfirmDialog(this, p_message, "确认",
                                                  JOptionPane.YES_NO_OPTION);
        return result == JOptionPane.YES_OPTION;
    }
}
```

## 🎨 用户交互设计

### 交互状态机

```mermaid
stateDiagram-v2
    [*] --> MenuState
    MenuState --> SelectState : 选择工具
    MenuState --> RouteState : 布线工具
    MenuState --> MoveState : 移动工具
    
    SelectState --> MenuState : ESC键
    SelectState --> SelectedItemState : 选中对象
    
    RouteState --> MenuState : ESC键
    RouteState --> DynamicRouteState : 开始布线
    
    SelectedItemState --> MenuState : 取消选择
    SelectedItemState --> MoveState : 移动对象
    SelectedItemState --> CopyState : 复制对象
    
    DynamicRouteState --> RouteState : 完成布线
    DynamicRouteState --> MenuState : 取消布线
```

### 键盘快捷键

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| Ctrl+O | 打开文件 | 打开设计文件 |
| Ctrl+S | 保存文件 | 保存当前设计 |
| Ctrl+Z | 撤销 | 撤销上一步操作 |
| Ctrl+Y | 重做 | 重做上一步操作 |
| F | 适应窗口 | 缩放到适合窗口大小 |
| + | 放大 | 放大视图 |
| - | 缩小 | 缩小视图 |
| Space | 旋转 | 旋转选中对象 |
| Delete | 删除 | 删除选中对象 |
| ESC | 取消 | 取消当前操作 |

## 🔧 自定义组件

### 1. 颜色管理器
```java
public class ColorManager {
    // 预定义颜色方案
    private static final Color[] LAYER_COLORS = {
        Color.RED, Color.BLUE, Color.GREEN, Color.YELLOW
    };
    
    public static Color get_layer_color(int layer_index) {
        return LAYER_COLORS[layer_index % LAYER_COLORS.length];
    }
}
```

### 2. 工具栏按钮
```java
public class SegmentedButtons extends JPanel {
    private List<JToggleButton> buttons = new ArrayList<>();
    private ButtonGroup button_group = new ButtonGroup();
    
    public void add_button(String text, ActionListener listener) {
        JToggleButton button = new JToggleButton(text);
        button.addActionListener(listener);
        button_group.add(button);
        buttons.add(button);
        this.add(button);
    }
}
```

## 📱 响应式设计

### 窗口大小适配
- **最小尺寸**: 800x600像素
- **推荐尺寸**: 1200x800像素
- **自动缩放**: 根据屏幕DPI调整UI元素大小
- **布局管理**: 使用BorderLayout和GridBagLayout

### 高DPI支持
- **图标缩放**: 自动选择合适分辨率的图标
- **字体缩放**: 根据系统设置调整字体大小
- **绘图精度**: 使用浮点坐标保证绘图精度

这个GUI模块为freerouting提供了专业而用户友好的界面，支持复杂的PCB设计和布线操作。
