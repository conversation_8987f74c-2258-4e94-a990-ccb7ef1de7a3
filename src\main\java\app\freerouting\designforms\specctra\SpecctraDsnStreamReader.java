package app.freerouting.designforms.specctra;

import app.freerouting.logger.FRLogger;

import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

@SuppressWarnings("all")

    /**
     * This class is a scanner generated by <a href="http://www.jflex.de/">JFlex</a> 1.4.1 on 16.10.2008 09:27 from the specification file <tt>SpecctraFileDescription.flex</tt>
     */
class SpecctraDsnStreamReader implements IJFlexScanner
{
  /**
   * This character denotes the end of file
   */
  public static final int YYEOF = -1;
  /**
   * lexical states
   */
  public static final int COMPONENT_NAME = 5;
  public static final int IGNORE_QUOTE = 7;
  public static final int YYINITIAL = 0;
  public static final int SPEC_CHAR = 6;
  public static final int LAYER_NAME = 4;
  public static final int STRING2 = 2;
  public static final int STRING1 = 1;
  public static final int NAME = 3;
  /**
   * initial size of the lookahead buffer
   */
  private static final int ZZ_BUFFERSIZE = 16 * 1024 * 1024;
  /**
   * Translates characters to character classes
   */
  private static final String ZZ_CMAP_PACKED = "\11\0\1\3\1\2\1\0\1\3\1\1\21\0\1\16\1\3\1\16" + "\1\11\1\6\1\16\1\16\1\16\1\12\1\53\1\54\1\5\1\20" + "\1\16\1\17\1\14\1\4\1\21\11\10\1\16\1\16\1\16\1\16" + "\1\16\1\16\1\16\1\23\1\24\1\32\1\42\1\22\1\41\1\35" + "\1\43\1\33\1\7\1\44\1\27\1\46\1\36\1\26\1\37\1\52" + "\1\40\1\25\1\31\1\30\1\34\1\51\1\47\1\45\1\50\1\16" + "\1\15\1\16\1\16\1\13\1\0\1\23\1\24\1\32\1\42\1\22" + "\1\41\1\35\1\43\1\33\1\7\1\44\1\27\1\46\1\36\1\26" + "\1\37\1\52\1\40\1\25\1\31\1\30\1\34\1\51\1\47\1\45" + "\1\50\3\0\1\16\42\0\136\16\1\16\122\0\2\16\14\0\2\16" + "\26\0\1\16\4\0\2\16\22\0\2\16\u0133\0\1\16\25\0\1\16" + "\u1d36\0\1\16\1\16\3\0\1\16\1\16\1\16\1\0\1\16\1\16" + "\1\16\1\0\1\16\1\16\1\16\3\0\1\16\11\0\1\16\10\0" + "\1\16\1\16\161\0\1\16\165\0\1\16\udedd\0";
  /**
   * Translates characters to character classes
   */
  private static final char[] ZZ_CMAP = zzUnpackCMap(ZZ_CMAP_PACKED);
  private static final String ZZ_ACTION_PACKED_0 = "\7\0\1\1\1\2\2\3\3\4\1\5\1\6\1\7" + "\1\2\1\5\22\4\1\10\1\11\1\12\1\13\1\14" + "\1\12\1\1\1\15\1\16\3\1\2\4\3\0\1\17" + "\16\4\1\20\45\4\2\1\1\0\1\4\2\17\1\0" + "\1\17\22\4\1\21\16\4\1\22\1\4\1\23\13\4" + "\1\24\1\25\11\4\1\26\10\4\1\1\1\27\1\0" + "\6\4\1\30\6\4\1\31\14\4\1\32\2\4\1\33" + "\2\4\1\34\3\4\1\35\2\4\1\36\2\4\1\37" + "\5\4\1\40\4\4\1\41\2\4\1\42\2\4\1\43" + "\11\4\1\44\3\4\1\1\14\4\1\45\1\46\2\4" + "\1\47\1\4\1\50\11\4\1\51\1\52\2\4\1\53" + "\20\4\1\54\1\55\1\56\6\4\1\57\3\4\1\60" + "\7\4\1\61\1\4\1\1\2\4\1\62\1\63\6\4" + "\1\64\3\4\1\65\15\4\1\34\10\4\1\66\1\4" + "\1\67\15\4\1\70\1\71\1\72\2\4\1\73\4\4" + "\1\74\1\4\1\75\1\4\1\76\4\4\1\77\6\4" + "\1\100\3\4\1\101\1\4\1\102\1\103\1\4\1\104" + "\3\4\1\105\1\106\6\4\1\107\6\4\1\40\17\4" + "\1\110\2\4\1\111\2\4\1\112\12\4\1\113\3\4" + "\1\114\2\4\1\115\5\4\1\116\1\4\1\117\14\4" + "\1\120\4\4\1\121\3\4\1\122\4\4\1\123\1\4" + "\1\124\1\33\1\51\2\4\1\125\5\4\1\126\3\4" + "\1\127\4\4\1\42\14\4\1\130\1\4\1\131\3\4" + "\1\132\11\4\1\133\1\4\1\134\2\4\1\135\1\4" + "\1\136\7\4\1\137\2\4\1\140\1\141\1\4\1\142" + "\20\4\1\143\1\144\3\4\1\145\5\4\1\146\1\4" + "\1\147\1\150\3\4\1\151\4\4\1\152\1\153\1\154" + "\1\155\23\4\1\156\1\4\1\157\11\4\1\160\1\161" + "\2\4\1\162\3\4\1\163\3\4\1\164\5\4\1\165" + "\6\4\1\166\20\4\1\167\7\4\1\170";
  /**
   * Translates DFA states to action switch labels.
   */
  private static final int[] ZZ_ACTION = zzUnpackAction();
  private static final String ZZ_ROWMAP_PACKED_0 = "\0\0\0\55\0\132\0\207\0\264\0\341\0\u010e\0\u013b" + "\0\u0168\0\u0195\0\u0168\0\u01c2\0\u01ef\0\u021c\0\u0249\0\u0168" + "\0\u0168\0\u0276\0\u02a3\0\u02d0\0\u02fd\0\u032a\0\u0357\0\u0384" + "\0\u03b1\0\u03de\0\u040b\0\u0438\0\u0465\0\u0492\0\u04bf\0\u04ec" + "\0\u0519\0\u0546\0\u0573\0\u05a0\0\u05cd\0\u0168\0\u0168\0\u05fa" + "\0\u0168\0\u0168\0\u0627\0\u0654\0\u0168\0\u0168\0\u0681\0\u06ae" + "\0\u06db\0\u0168\0\u0708\0\u0735\0\u0762\0\u078f\0\u02a3\0\u07bc" + "\0\u07e9\0\u0816\0\u0843\0\u0870\0\u089d\0\u08ca\0\u08f7\0\u0924" + "\0\u0951\0\u097e\0\u09ab\0\u09d8\0\u0a05\0\u01ef\0\u0a32\0\u0a5f" + "\0\u0a8c\0\u0ab9\0\u0ae6\0\u0b13\0\u0b40\0\u0b6d\0\u0b9a\0\u0bc7" + "\0\u0bf4\0\u0c21\0\u0c4e\0\u0c7b\0\u0ca8\0\u0cd5\0\u0d02\0\u0d2f" + "\0\u0d5c\0\u0d89\0\u0db6\0\u0de3\0\u0e10\0\u0e3d\0\u0e6a\0\u0e97" + "\0\u0ec4\0\u0ef1\0\u0f1e\0\u0f4b\0\u0f78\0\u0fa5\0\u0fd2\0\u0fff" + "\0\u102c\0\u1059\0\u1086\0\u10b3\0\u10e0\0\u110d\0\u113a\0\u1167" + "\0\u1194\0\u11c1\0\u0168\0\u11ee\0\u121b\0\u1248\0\u1275\0\u12a2" + "\0\u12cf\0\u12fc\0\u1329\0\u1356\0\u1383\0\u13b0\0\u13dd\0\u140a" + "\0\u1437\0\u1464\0\u1491\0\u14be\0\u14eb\0\u01ef\0\u1518\0\u1545" + "\0\u1572\0\u159f\0\u15cc\0\u15f9\0\u1626\0\u1653\0\u1680\0\u16ad" + "\0\u16da\0\u1707\0\u1734\0\u1761\0\u178e\0\u17bb\0\u17e8\0\u1815" + "\0\u1842\0\u186f\0\u189c\0\u18c9\0\u18f6\0\u1923\0\u1950\0\u197d" + "\0\u19aa\0\u19d7\0\u01ef\0\u1a04\0\u1a31\0\u1a5e\0\u1a8b\0\u1ab8" + "\0\u1ae5\0\u1b12\0\u1b3f\0\u1b6c\0\u1b99\0\u01ef\0\u1bc6\0\u1bf3" + "\0\u1c20\0\u1c4d\0\u1c7a\0\u1ca7\0\u1cd4\0\u1d01\0\u1d2e\0\u0654" + "\0\u1d5b\0\u1d88\0\u1db5\0\u1de2\0\u1e0f\0\u1e3c\0\u1e69\0\u01ef" + "\0\u1e96\0\u1ec3\0\u1ef0\0\u1f1d\0\u1f4a\0\u1f77\0\u01ef\0\u1fa4" + "\0\u1fd1\0\u1ffe\0\u202b\0\u2058\0\u2085\0\u20b2\0\u20df\0\u210c" + "\0\u2139\0\u2166\0\u2193\0\u01ef\0\u21c0\0\u21ed\0\u221a\0\u2247" + "\0\u2274\0\u22a1\0\u22ce\0\u22fb\0\u2328\0\u01ef\0\u2355\0\u2382" + "\0\u01ef\0\u23af\0\u23dc\0\u01ef\0\u2409\0\u2436\0\u2463\0\u2490" + "\0\u24bd\0\u24ea\0\u2517\0\u2544\0\u2571\0\u259e\0\u01ef\0\u25cb" + "\0\u25f8\0\u2625\0\u2652\0\u267f\0\u26ac\0\u26d9\0\u2706\0\u2733" + "\0\u2760\0\u278d\0\u27ba\0\u27e7\0\u2814\0\u2841\0\u286e\0\u289b" + "\0\u28c8\0\u28f5\0\u2922\0\u294f\0\u297c\0\u29a9\0\u29d6\0\u2a03" + "\0\u2a30\0\u2a5d\0\u2a8a\0\u2ab7\0\u2ae4\0\u2b11\0\u2b3e\0\u01ef" + "\0\u01ef\0\u2b6b\0\u2b98\0\u01ef\0\u2bc5\0\u2bf2\0\u2c1f\0\u2c4c" + "\0\u2c79\0\u2ca6\0\u2cd3\0\u2d00\0\u2d2d\0\u2d5a\0\u2d87\0\u2db4" + "\0\u2de1\0\u2e0e\0\u2e3b\0\u01ef\0\u2e68\0\u2e95\0\u2ec2\0\u2eef" + "\0\u2f1c\0\u2f49\0\u2f76\0\u2fa3\0\u2fd0\0\u2ffd\0\u302a\0\u3057" + "\0\u3084\0\u30b1\0\u30de\0\u310b\0\u01ef\0\u3138\0\u3165\0\u3192" + "\0\u31bf\0\u31ec\0\u3219\0\u3246\0\u3273\0\u01ef\0\u32a0\0\u32cd" + "\0\u32fa\0\u01ef\0\u3327\0\u3354\0\u3381\0\u33ae\0\u33db\0\u3408" + "\0\u3435\0\u01ef\0\u3462\0\u348f\0\u34bc\0\u34e9\0\u01ef\0\u01ef" + "\0\u3516\0\u3543\0\u3570\0\u359d\0\u35ca\0\u35f7\0\u01ef\0\u3624" + "\0\u3651\0\u367e\0\u01ef\0\u36ab\0\u36d8\0\u3705\0\u3732\0\u375f" + "\0\u378c\0\u37b9\0\u37e6\0\u3813\0\u3840\0\u386d\0\u389a\0\u38c7" + "\0\u01ef\0\u38f4\0\u3921\0\u394e\0\u397b\0\u39a8\0\u39d5\0\u3a02" + "\0\u3a2f\0\u01ef\0\u3a5c\0\u01ef\0\u3a89\0\u3ab6\0\u3ae3\0\u3b10" + "\0\u3b3d\0\u3b6a\0\u3b97\0\u3bc4\0\u3bf1\0\u3c1e\0\u3c4b\0\u3c78" + "\0\u3ca5\0\u01ef\0\u3cd2\0\u01ef\0\u3cff\0\u3d2c\0\u01ef\0\u3d59" + "\0\u3d86\0\u3db3\0\u3de0\0\u01ef\0\u3e0d\0\u01ef\0\u3e3a\0\u0654" + "\0\u3e67\0\u3e94\0\u3ec1\0\u3eee\0\u01ef\0\u3f1b\0\u3f48\0\u3f75" + "\0\u3fa2\0\u3fcf\0\u3ffc\0\u01ef\0\u4029\0\u4056\0\u4083\0\u01ef" + "\0\u40b0\0\u01ef\0\u01ef\0\u40dd\0\u01ef\0\u410a\0\u4137\0\u4164" + "\0\u01ef\0\u01ef\0\u4191\0\u41be\0\u41eb\0\u4218\0\u4245\0\u4272" + "\0\u429f\0\u42cc\0\u42f9\0\u4326\0\u4353\0\u4380\0\u43ad\0\u01ef" + "\0\u43da\0\u4407\0\u4434\0\u4461\0\u448e\0\u44bb\0\u44e8\0\u4515" + "\0\u4542\0\u456f\0\u459c\0\u45c9\0\u45f6\0\u4623\0\u4650\0\u01ef" + "\0\u467d\0\u46aa\0\u01ef\0\u46d7\0\u4704\0\u01ef\0\u4731\0\u475e" + "\0\u478b\0\u47b8\0\u47e5\0\u4812\0\u483f\0\u486c\0\u4899\0\u48c6" + "\0\u01ef\0\u48f3\0\u4920\0\u494d\0\u01ef\0\u497a\0\u49a7\0\u01ef" + "\0\u49d4\0\u4a01\0\u4a2e\0\u4a5b\0\u4a88\0\u01ef\0\u4ab5\0\u01ef" + "\0\u4ae2\0\u4b0f\0\u4b3c\0\u4b69\0\u4b96\0\u4bc3\0\u4bf0\0\u4c1d" + "\0\u4c4a\0\u4c77\0\u4ca4\0\u4cd1\0\u01ef\0\u4cfe\0\u4d2b\0\u4d58" + "\0\u4d85\0\u4db2\0\u4ddf\0\u4e0c\0\u4e39\0\u01ef\0\u4e66\0\u4e93" + "\0\u4ec0\0\u4eed\0\u01ef\0\u4f1a\0\u01ef\0\u01ef\0\u4f47\0\u4f74" + "\0\u4fa1\0\u01ef\0\u4fce\0\u4ffb\0\u5028\0\u5055\0\u5082\0\u01ef" + "\0\u50af\0\u50dc\0\u5109\0\u01ef\0\u5136\0\u5163\0\u5190\0\u51bd" + "\0\u01ef\0\u51ea\0\u5217\0\u5244\0\u5271\0\u529e\0\u52cb\0\u52f8" + "\0\u5325\0\u5352\0\u537f\0\u53ac\0\u53d9\0\u01ef\0\u5406\0\u01ef" + "\0\u5433\0\u5460\0\u548d\0\u01ef\0\u54ba\0\u54e7\0\u5514\0\u5541" + "\0\u556e\0\u559b\0\u55c8\0\u55f5\0\u5622\0\u01ef\0\u564f\0\u01ef" + "\0\u567c\0\u56a9\0\u01ef\0\u56d6\0\u01ef\0\u5703\0\u5730\0\u575d" + "\0\u578a\0\u57b7\0\u57e4\0\u5811\0\u01ef\0\u583e\0\u586b\0\u01ef" + "\0\u01ef\0\u5898\0\u01ef\0\u58c5\0\u58f2\0\u591f\0\u594c\0\u5979" + "\0\u59a6\0\u59d3\0\u5a00\0\u5a2d\0\u5a5a\0\u5a87\0\u5ab4\0\u5ae1" + "\0\u5b0e\0\u5b3b\0\u5b68\0\u01ef\0\u5b95\0\u5bc2\0\u5bef\0\u5c1c" + "\0\u01ef\0\u5c49\0\u5c76\0\u5ca3\0\u5cd0\0\u5cfd\0\u01ef\0\u5d2a" + "\0\u01ef\0\u01ef\0\u5d57\0\u5d84\0\u5db1\0\u01ef\0\u5dde\0\u5e0b" + "\0\u5e38\0\u5e65\0\u01ef\0\u01ef\0\u01ef\0\u01ef\0\u5e92\0\u5ebf" + "\0\u5eec\0\u5f19\0\u5f46\0\u5f73\0\u5fa0\0\u5fcd\0\u5ffa\0\u6027" + "\0\u6054\0\u6081\0\u60ae\0\u60db\0\u6108\0\u6135\0\u6162\0\u618f" + "\0\u61bc\0\u01ef\0\u61e9\0\u01ef\0\u6216\0\u6243\0\u6270\0\u629d" + "\0\u62ca\0\u62f7\0\u6324\0\u6351\0\u637e\0\u01ef\0\u01ef\0\u63ab" + "\0\u63d8\0\u01ef\0\u6405\0\u6432\0\u645f\0\u01ef\0\u648c\0\u64b9" + "\0\u64e6\0\u6513\0\u6540\0\u656d\0\u659a\0\u65c7\0\u65f4\0\u01ef" + "\0\u6621\0\u664e\0\u667b\0\u66a8\0\u66d5\0\u6702\0\u01ef\0\u672f" + "\0\u675c\0\u6789\0\u67b6\0\u67e3\0\u6810\0\u683d\0\u686a\0\u6897" + "\0\u68c4\0\u68f1\0\u691e\0\u694b\0\u6978\0\u69a5\0\u69d2\0\u01ef" + "\0\u69ff\0\u6a2c\0\u6a59\0\u6a86\0\u6ab3\0\u6ae0\0\u6b0d\0\u01ef";
  /**
   * Translates a state to a row index in the transition table
   */
  private static final int[] ZZ_ROWMAP = zzUnpackRowMap();
  private static final String ZZ_TRANS_PACKED_0 = "\1\11\1\12\2\13\1\14\1\15\1\16\1\15\1\17" + "\1\20\1\21\4\15\2\22\1\23\1\15\1\24\1\25" + "\1\26\1\27\1\30\1\31\1\32\1\33\1\34\1\35" + "\1\36\1\37\1\40\1\41\1\42\1\15\1\43\1\44" + "\4\15\1\45\1\15\1\46\1\47\11\50\1\51\3\50" + "\1\52\37\50\12\53\1\51\2\53\1\52\37\53\1\11" + "\1\12\2\13\5\54\1\20\1\21\40\54\1\55\1\56" + "\1\11\1\12\2\13\5\54\1\20\1\21\12\54\1\57" + "\11\54\1\60\13\54\1\55\1\56\1\11\1\12\2\13" + "\5\61\1\20\1\21\4\61\1\11\33\61\1\55\1\56" + "\4\11\3\62\4\11\6\62\35\11\1\12\2\13\47\54" + "\1\55\1\56\57\0\1\13\56\0\1\15\1\63\45\15" + "\6\0\47\15\2\0\1\64\1\12\1\13\1\64\47\16" + "\2\64\10\0\1\17\3\0\1\65\4\0\1\17\1\66" + "\42\0\1\17\10\0\1\23\43\0\1\67\3\0\1\65" + "\4\0\1\67\1\66\36\0\20\15\1\70\3\15\1\71" + "\1\72\1\73\2\15\1\74\15\15\6\0\17\15\1\75" + "\2\15\1\76\24\15\6\0\16\15\1\77\6\15\1\100" + "\1\15\1\101\2\15\1\102\1\103\3\15\1\104\7\15" + "\6\0\24\15\1\105\5\15\1\106\1\15\1\107\1\110" + "\11\15\6\0\16\15\1\111\1\112\2\15\1\113\4\15" + "\1\114\17\15\6\0\21\15\1\115\25\15\6\0\41\15" + "\1\116\5\15\6\0\22\15\1\117\1\120\3\15\1\121" + "\17\15\6\0\42\15\1\122\4\15\6\0\16\15\1\123" + "\10\15\1\124\17\15\6\0\16\15\1\125\30\15\6\0" + "\16\15\1\126\3\15\1\127\4\15\1\130\17\15\6\0" + "\17\15\1\131\2\15\1\132\1\133\1\134\1\15\1\135" + "\1\136\4\15\1\137\12\15\6\0\16\15\1\140\3\15" + "\1\141\1\15\1\142\22\15\6\0\17\15\1\143\2\15" + "\1\144\1\145\3\15\1\146\4\15\1\147\12\15\6\0" + "\22\15\1\150\24\15\6\0\16\15\1\151\30\15\6\0" + "\27\15\1\152\4\15\1\153\12\15\2\0\11\50\1\0" + "\3\50\1\0\37\50\12\53\1\0\2\53\1\0\37\53" + "\4\0\47\54\6\0\27\54\1\154\17\54\6\0\26\54" + "\1\155\20\54\6\0\13\61\1\0\33\61\2\0\4\156" + "\1\157\1\15\45\157\2\156\1\64\1\12\1\13\52\64" + "\10\0\1\160\10\0\1\160\43\0\1\161\6\0\2\162" + "\1\163\37\0\21\15\1\164\25\15\6\0\25\15\1\165" + "\21\15\6\0\25\15\1\166\21\15\6\0\25\15\1\167" + "\21\15\6\0\17\15\1\170\27\15\6\0\26\15\1\171" + "\20\15\6\0\24\15\1\172\22\15\6\0\21\15\1\173" + "\25\15\6\0\17\15\1\174\14\15\1\175\12\15\6\0" + "\31\15\1\176\4\15\1\177\10\15\6\0\17\15\1\200" + "\27\15\6\0\17\15\1\201\27\15\6\0\17\15\1\202" + "\2\15\1\203\24\15\6\0\25\15\1\204\21\15\6\0" + "\36\15\1\205\10\15\6\0\35\15\1\206\11\15\6\0" + "\32\15\1\207\14\15\6\0\41\15\1\210\5\15\6\0" + "\26\15\1\211\2\15\1\212\15\15\6\0\20\15\1\213" + "\26\15\6\0\16\15\1\214\30\15\6\0\33\15\1\215" + "\13\15\6\0\32\15\1\216\7\15\1\217\4\15\6\0" + "\16\15\1\220\1\221\27\15\6\0\34\15\1\222\12\15" + "\6\0\17\15\1\223\27\15\6\0\34\15\1\224\12\15" + "\6\0\17\15\1\225\27\15\6\0\32\15\1\226\14\15" + "\6\0\25\15\1\227\21\15\6\0\32\15\1\230\1\15" + "\1\231\12\15\6\0\32\15\1\232\14\15\6\0\25\15" + "\1\233\6\15\1\234\1\15\1\235\10\15\6\0\21\15" + "\1\236\1\15\1\237\21\15\1\240\1\15\6\0\17\15" + "\1\241\27\15\6\0\23\15\1\242\23\15\6\0\20\15" + "\1\243\26\15\6\0\32\15\1\244\14\15\6\0\16\15" + "\1\245\30\15\6\0\21\15\1\246\4\15\1\247\20\15" + "\6\0\24\15\1\250\1\251\21\15\6\0\23\15\1\252" + "\23\15\6\0\32\15\1\253\14\15\6\0\34\15\1\254" + "\12\15\6\0\27\15\1\255\17\15\6\0\43\15\1\256" + "\3\15\6\0\22\15\1\257\24\15\6\0\21\15\1\260" + "\12\15\1\261\12\15\6\0\16\15\1\262\30\15\6\0" + "\32\15\1\263\1\15\1\264\1\15\1\265\10\15\6\0" + "\27\15\1\266\17\15\6\0\31\54\1\267\15\54\6\0" + "\20\54\1\270\26\54\2\0\5\156\1\271\53\156\1\157" + "\1\272\45\157\2\156\10\0\1\160\10\0\1\160\1\66" + "\42\0\1\161\10\0\1\161\43\0\1\161\10\0\1\163" + "\37\0\22\15\1\273\24\15\6\0\22\15\1\274\24\15" + "\6\0\17\15\1\275\27\15\6\0\27\15\1\276\17\15" + "\6\0\27\15\1\277\17\15\6\0\40\15\1\300\6\15" + "\6\0\32\15\1\301\14\15\6\0\21\15\1\302\25\15" + "\6\0\34\15\1\303\12\15\6\0\24\15\1\304\2\15" + "\1\305\17\15\6\0\32\15\1\306\14\15\6\0\16\15" + "\1\307\30\15\6\0\33\15\1\310\13\15\6\0\34\15" + "\1\311\12\15\6\0\33\15\1\312\13\15\6\0\30\15" + "\1\313\16\15\6\0\23\15\1\314\23\15\6\0\16\15" + "\1\315\30\15\6\0\31\15\1\316\15\15\6\0\16\15" + "\1\317\30\15\6\0\40\15\1\320\6\15\6\0\27\15" + "\1\321\17\15\6\0\34\15\1\322\12\15\6\0\7\15" + "\1\323\37\15\6\0\16\15\1\324\30\15\6\0\21\15" + "\1\325\3\15\1\326\21\15\6\0\33\15\1\327\13\15" + "\6\0\17\15\1\330\27\15\6\0\21\15\1\331\25\15" + "\6\0\26\15\1\332\20\15\6\0\31\15\1\333\15\15" + "\6\0\25\15\1\334\21\15\6\0\7\15\1\335\11\15" + "\1\336\25\15\6\0\16\15\1\337\30\15\6\0\45\15" + "\1\340\1\15\6\0\16\15\1\341\30\15\6\0\42\15" + "\1\342\4\15\6\0\16\15\1\343\30\15\6\0\37\15" + "\1\344\7\15\6\0\21\15\1\345\3\15\1\346\21\15" + "\6\0\21\15\1\347\25\15\6\0\25\15\1\350\1\15" + "\1\351\17\15\6\0\41\15\1\352\5\15\6\0\16\15" + "\1\353\30\15\6\0\26\15\1\354\3\15\1\355\14\15" + "\6\0\23\15\1\356\23\15\6\0\21\15\1\357\25\15" + "\6\0\35\15\1\360\11\15\6\0\22\15\1\361\24\15" + "\6\0\25\15\1\362\21\15\6\0\25\15\1\363\21\15" + "\6\0\17\15\1\364\27\15\6\0\16\15\1\365\30\15" + "\6\0\22\15\1\366\24\15\6\0\25\15\1\367\21\15" + "\6\0\33\15\1\370\13\15\6\0\32\15\1\371\7\15" + "\1\372\4\15\6\0\25\15\1\373\21\15\6\0\27\15" + "\1\374\17\15\6\0\33\15\1\375\13\15\6\0\36\15" + "\1\376\10\15\6\0\16\15\1\377\10\15\1\u0100\17\15" + "\6\0\25\15\1\u0101\21\15\6\0\25\15\1\u0102\21\15" + "\6\0\32\54\1\u0103\14\54\2\0\4\156\1\13\1\271" + "\53\156\1\15\1\272\45\157\2\156\4\0\23\15\1\u0104" + "\23\15\6\0\34\15\1\u0105\12\15\6\0\26\15\1\u0106" + "\20\15\6\0\30\15\1\u0107\16\15\6\0\32\15\1\u0108" + "\14\15\6\0\36\15\1\u0109\10\15\6\0\27\15\1\u010a" + "\17\15\6\0\25\15\1\u010b\21\15\6\0\26\15\1\u010c" + "\20\15\6\0\32\15\1\u010d\14\15\6\0\17\15\1\u010e" + "\27\15\6\0\7\15\1\u010f\37\15\6\0\16\15\1\u0110" + "\30\15\6\0\16\15\1\u0111\30\15\6\0\16\15\1\u0112" + "\30\15\6\0\27\15\1\u0113\17\15\6\0\34\15\1\u0114" + "\12\15\6\0\25\15\1\u0115\21\15\6\0\34\15\1\u0116" + "\12\15\6\0\7\15\1\u0117\37\15\6\0\26\15\1\u0118" + "\20\15\6\0\17\15\1\u0119\27\15\6\0\23\15\1\u011a" + "\4\15\1\u011b\1\15\1\u011c\14\15\6\0\25\15\1\u011d" + "\21\15\6\0\34\15\1\u011e\12\15\6\0\22\15\1\u011f" + "\24\15\6\0\34\15\1\u0120\12\15\6\0\21\15\1\u0121" + "\25\15\6\0\23\15\1\u0122\1\u0123\22\15\6\0\16\15" + "\1\u0124\30\15\6\0\27\15\1\u0125\17\15\6\0\17\15" + "\1\u0126\6\15\1\u0127\5\15\1\u0128\3\15\1\u0129\6\15" + "\6\0\34\15\1\u012a\12\15\6\0\22\15\1\u012b\24\15" + "\6\0\17\15\1\u012c\27\15\6\0\25\15\1\u012d\21\15" + "\6\0\16\15\1\u012e\30\15\6\0\7\15\1\u012f\37\15" + "\6\0\25\15\1\u0130\21\15\6\0\34\15\1\u0131\12\15" + "\6\0\25\15\1\u0132\21\15\6\0\23\15\1\u0133\5\15" + "\1\u0134\15\15\6\0\34\15\1\u0135\12\15\6\0\16\15" + "\1\u0136\30\15\6\0\16\15\1\u0137\30\15\6\0\7\15" + "\1\u0138\37\15\6\0\16\15\1\u0139\30\15\6\0\23\15" + "\1\u013a\23\15\6\0\17\15\1\u013b\27\15\6\0\16\15" + "\1\u013c\30\15\6\0\25\15\1\u013d\21\15\6\0\21\15" + "\1\u013e\25\15\6\0\24\15\1\u013f\22\15\6\0\41\15" + "\1\u0140\5\15\6\0\7\15\1\u0141\37\15\6\0\25\15" + "\1\u0142\21\15\6\0\25\15\1\u0143\21\15\6\0\7\15" + "\1\u0144\37\15\6\0\44\15\1\u0145\2\15\6\0\22\15" + "\1\u0146\24\15\6\0\22\15\1\u0147\24\15\6\0\7\15" + "\1\u0148\37\15\6\0\32\15\1\u0149\14\15\6\0\37\15" + "\1\u014a\7\15\6\0\16\15\1\u014b\30\15\6\0\17\54" + "\1\u014c\27\54\6\0\24\15\1\u014d\22\15\6\0\22\15" + "\1\u014e\24\15\6\0\37\15\1\u014f\7\15\6\0\16\15" + "\1\u0150\30\15\6\0\21\15\1\u0151\25\15\6\0\17\15" + "\1\u0152\27\15\6\0\22\15\1\u0153\24\15\6\0\7\15" + "\1\u0154\37\15\6\0\25\15\1\u0155\21\15\6\0\31\15" + "\1\u0156\15\15\6\0\23\15\1\u0157\23\15\6\0\17\15" + "\1\u0158\27\15\6\0\7\15\1\u0159\37\15\6\0\32\15" + "\1\u015a\14\15\6\0\37\15\1\u015b\7\15\6\0\7\15" + "\1\u015c\37\15\6\0\25\15\1\u015d\21\15\6\0\17\15" + "\1\u015e\27\15\6\0\34\15\1\u015f\12\15\6\0\17\15" + "\1\u0160\27\15\6\0\27\15\1\u0161\17\15\6\0\16\15" + "\1\u0162\30\15\6\0\17\15\1\u0163\27\15\6\0\22\15" + "\1\u0164\24\15\6\0\32\15\1\u0165\14\15\6\0\17\15" + "\1\u0166\27\15\6\0\7\15\1\u0167\6\15\1\u0168\30\15" + "\6\0\16\15\1\u0169\30\15\6\0\27\15\1\u016a\17\15" + "\6\0\26\15\1\u016b\20\15\6\0\25\15\1\u016c\21\15" + "\6\0\22\15\1\u016d\24\15\6\0\24\15\1\u016e\22\15" + "\6\0\16\15\1\u016f\30\15\6\0\17\15\1\u0170\27\15" + "\6\0\34\15\1\u0171\12\15\6\0\23\15\1\u0172\23\15" + "\6\0\41\15\1\u0173\5\15\6\0\34\15\1\u0174\12\15" + "\6\0\23\15\1\u0175\23\15\6\0\17\15\1\u0176\27\15" + "\6\0\22\15\1\u0177\24\15\6\0\27\15\1\u0178\17\15" + "\6\0\27\15\1\u0179\17\15\6\0\22\15\1\u017a\24\15" + "\6\0\7\15\1\u017b\32\15\1\u017c\4\15\6\0\7\15" + "\1\u017d\37\15\6\0\25\15\1\u017e\21\15\6\0\34\15" + "\1\u017f\12\15\6\0\24\15\1\u0180\22\15\6\0\32\15" + "\1\u0181\14\15\6\0\21\15\1\u0182\25\15\6\0\16\15" + "\1\u0183\30\15\6\0\25\15\1\u0184\21\15\6\0\35\15" + "\1\u0185\11\15\6\0\21\15\1\u0186\25\15\6\0\22\15" + "\1\u0187\24\15\6\0\26\15\1\u0188\1\15\1\u0189\16\15" + "\6\0\22\15\1\u018a\24\15\6\0\24\15\1\u018b\22\15" + "\6\0\45\15\1\u018c\1\15\6\0\40\15\1\u018d\6\15" + "\6\0\31\15\1\u018e\15\15\6\0\7\15\1\u018f\37\15" + "\6\0\23\54\1\u0190\23\54\6\0\25\15\1\u0191\21\15" + "\6\0\24\15\1\u0192\22\15\6\0\25\15\1\u0193\21\15" + "\6\0\34\15\1\u0194\12\15\6\0\32\15\1\u0195\14\15" + "\6\0\33\15\1\u0196\1\u0197\12\15\6\0\24\15\1\u0198" + "\22\15\6\0\7\15\1\u0199\37\15\6\0\32\15\1\u019a" + "\14\15\6\0\35\15\1\u019b\11\15\6\0\16\15\1\u019c" + "\30\15\6\0\34\15\1\u019d\12\15\6\0\41\15\1\u019e" + "\5\15\6\0\23\15\1\u019f\23\15\6\0\41\15\1\u01a0" + "\5\15\6\0\41\15\1\u01a1\5\15\6\0\17\15\1\u01a2" + "\27\15\6\0\25\15\1\u01a3\21\15\6\0\32\15\1\u01a4" + "\14\15\6\0\23\15\1\u01a5\23\15\6\0\16\15\1\u01a6" + "\30\15\6\0\32\15\1\u01a7\14\15\6\0\26\15\1\u01a8" + "\20\15\6\0\21\15\1\u01a9\25\15\6\0\25\15\1\u01aa" + "\21\15\6\0\17\15\1\u01ab\27\15\6\0\7\15\1\u01ac" + "\37\15\6\0\21\15\1\u01ad\25\15\6\0\23\15\1\u01ae" + "\23\15\6\0\16\15\1\u01af\30\15\6\0\25\15\1\u01b0" + "\21\15\6\0\40\15\1\u01b1\6\15\6\0\7\15\1\u01b2" + "\37\15\6\0\27\15\1\u01b3\17\15\6\0\26\15\1\u01b4" + "\20\15\6\0\24\15\1\u01b5\22\15\6\0\22\15\1\u01b6" + "\24\15\6\0\32\15\1\u01b7\14\15\6\0\32\15\1\u01b8" + "\14\15\6\0\26\15\1\u01b9\11\15\1\u01ba\6\15\6\0" + "\16\15\1\u01bb\30\15\6\0\30\15\1\u01bc\16\15\6\0" + "\27\15\1\u01bd\17\15\6\0\16\15\1\u01be\15\15\1\u01bf" + "\12\15\6\0\25\15\1\u01c0\21\15\6\0\31\15\1\u01c1" + "\15\15\6\0\7\15\1\u01c2\37\15\6\0\27\15\1\u01c3" + "\17\15\6\0\25\15\1\u01c4\21\15\6\0\17\15\1\u01c5" + "\27\15\6\0\16\15\1\u01c6\30\15\6\0\32\15\1\u01c7" + "\14\15\6\0\25\15\1\u01c8\21\15\6\0\16\15\1\u01c9" + "\30\15\6\0\34\15\1\u01ca\12\15\6\0\16\15\1\u01cb" + "\30\15\6\0\25\15\1\u01cc\21\15\6\0\7\15\1\u01cd" + "\37\15\6\0\41\15\1\u01ce\5\15\6\0\17\15\1\u01cf" + "\27\15\6\0\27\15\1\u01d0\17\15\6\0\34\15\1\u01d1" + "\12\15\6\0\46\15\1\u01d2\6\0\31\15\1\u01d3\15\15" + "\6\0\27\15\1\u01d4\17\15\6\0\24\15\1\u01d5\22\15" + "\6\0\33\15\1\u01d6\13\15\6\0\7\15\1\u01d7\37\15" + "\6\0\16\15\1\u01d8\30\15\6\0\25\15\1\u01d9\21\15" + "\6\0\32\15\1\u01da\14\15\6\0\26\15\1\u01db\20\15" + "\6\0\23\15\1\u01dc\23\15\6\0\23\15\1\u01dd\23\15" + "\6\0\21\15\1\u01de\25\15\6\0\25\15\1\u01df\21\15" + "\6\0\16\15\1\u01e0\30\15\6\0\33\15\1\u01e1\13\15" + "\6\0\16\15\1\u01e2\30\15\6\0\7\15\1\u01e3\37\15" + "\6\0\36\15\1\u01e4\10\15\6\0\20\15\1\u01e5\26\15" + "\6\0\40\15\1\u01e6\6\15\6\0\25\15\1\u01e7\21\15" + "\6\0\32\15\1\u01e8\14\15\6\0\16\15\1\u01e9\30\15" + "\6\0\22\15\1\u01ea\24\15\6\0\16\15\1\u01eb\30\15" + "\6\0\32\15\1\u01ec\14\15\6\0\27\15\1\u01ed\17\15" + "\6\0\31\15\1\u01ee\15\15\6\0\36\15\1\u01ef\10\15" + "\6\0\16\15\1\u01be\30\15\6\0\27\15\1\u01f0\17\15" + "\6\0\23\15\1\u01f1\23\15\6\0\35\15\1\u01f2\11\15" + "\6\0\30\15\1\u01f3\16\15\6\0\41\15\1\u01f4\5\15" + "\6\0\36\15\1\u01f5\10\15\6\0\34\15\1\u01f6\12\15" + "\6\0\25\15\1\u01f7\21\15\6\0\16\15\1\u01f8\30\15" + "\6\0\16\15\1\u01f9\30\15\6\0\16\15\1\u01fa\30\15" + "\6\0\33\15\1\u01fb\13\15\6\0\21\15\1\u01fc\25\15" + "\6\0\33\15\1\u01fd\13\15\6\0\16\15\1\u01fe\30\15" + "\6\0\24\15\1\u01ff\22\15\6\0\23\15\1\u0200\23\15" + "\6\0\43\15\1\u0201\3\15\6\0\23\15\1\u0202\23\15" + "\6\0\16\15\1\u0203\30\15\6\0\33\15\1\u0204\13\15" + "\6\0\34\15\1\u0205\12\15\6\0\25\15\1\u0206\21\15" + "\6\0\16\15\1\u0207\30\15\6\0\17\15\1\u0208\27\15" + "\6\0\42\15\1\u0209\4\15\6\0\21\15\1\u020a\25\15" + "\6\0\22\15\1\u020b\24\15\6\0\36\15\1\u020c\10\15" + "\6\0\22\15\1\u020d\24\15\6\0\16\15\1\u020e\30\15" + "\6\0\34\15\1\u020f\12\15\6\0\16\15\1\u0210\30\15" + "\6\0\7\15\1\u0211\37\15\6\0\32\15\1\u0212\14\15" + "\6\0\16\15\1\u0213\30\15\6\0\25\15\1\u0214\21\15" + "\6\0\17\15\1\u0215\27\15\6\0\37\15\1\u0216\7\15" + "\6\0\7\15\1\u0217\37\15\6\0\22\15\1\u0218\24\15" + "\6\0\16\15\1\u0219\30\15\6\0\27\15\1\u021a\17\15" + "\6\0\16\15\1\u021b\30\15\6\0\23\15\1\u021c\23\15" + "\6\0\21\15\1\u021d\25\15\6\0\17\15\1\u021e\27\15" + "\6\0\33\15\1\u021f\13\15\6\0\21\15\1\u0220\25\15" + "\6\0\7\15\1\u0221\37\15\6\0\34\15\1\u0222\12\15" + "\6\0\21\15\1\u0223\25\15\6\0\24\15\1\u0224\22\15" + "\6\0\22\15\1\u0225\24\15\6\0\16\15\1\u0226\30\15" + "\6\0\16\15\1\u0227\30\15\6\0\16\15\1\u0228\30\15" + "\6\0\17\15\1\u0229\27\15\6\0\7\15\1\u022a\37\15" + "\6\0\21\15\1\u022b\25\15\6\0\36\15\1\u022c\10\15" + "\6\0\24\15\1\u022d\22\15\6\0\7\15\1\u022e\37\15" + "\6\0\24\15\1\u022f\22\15\6\0\31\15\1\u0230\15\15" + "\6\0\17\15\1\u0231\27\15\6\0\33\15\1\u0232\13\15" + "\6\0\25\15\1\u0233\21\15\6\0\33\15\1\u0234\13\15" + "\6\0\7\15\1\u0235\37\15\6\0\25\15\1\u0236\21\15" + "\6\0\36\15\1\u0237\10\15\6\0\32\15\1\u0238\14\15" + "\6\0\34\15\1\u0239\12\15\6\0\7\15\1\u023a\37\15" + "\6\0\16\15\1\u023b\30\15\6\0\27\15\1\u023c\17\15" + "\6\0\23\15\1\u023d\23\15\6\0\22\15\1\u023e\24\15" + "\6\0\22\15\1\u023f\24\15\6\0\21\15\1\u0240\25\15" + "\6\0\16\15\1\u0241\30\15\6\0\7\15\1\u0242\37\15" + "\6\0\33\15\1\u0243\13\15\6\0\25\15\1\u0244\21\15" + "\6\0\36\15\1\u0245\10\15\6\0\34\15\1\u0246\12\15" + "\6\0\26\15\1\u0247\20\15\6\0\21\15\1\u0248\25\15" + "\6\0\25\15\1\u0249\21\15\6\0\20\15\1\u024a\26\15" + "\6\0\25\15\1\u024b\21\15\6\0\34\15\1\u024c\12\15" + "\6\0\34\15\1\u024d\12\15\6\0\17\15\1\u024e\27\15" + "\6\0\34\15\1\u024f\12\15\6\0\22\15\1\u0250\24\15" + "\6\0\26\15\1\u0251\20\15\6\0\27\15\1\u0252\17\15" + "\6\0\21\15\1\u0253\25\15\6\0\36\15\1\u0254\10\15" + "\6\0\22\15\1\u0255\24\15\6\0\24\15\1\u0256\22\15" + "\6\0\23\15\1\u0257\23\15\6\0\16\15\1\u0258\30\15" + "\6\0\35\15\1\u0259\11\15\6\0\32\15\1\u025a\14\15" + "\6\0\7\15\1\u025b\37\15\6\0\16\15\1\u025c\30\15" + "\6\0\25\15\1\u025d\21\15\6\0\23\15\1\u025e\23\15" + "\6\0\41\15\1\u025f\5\15\6\0\16\15\1\u0260\30\15" + "\6\0\41\15\1\u0261\5\15\6\0\25\15\1\u0262\21\15" + "\6\0\22\15\1\u0263\24\15\6\0\24\15\1\u0264\22\15" + "\6\0\22\15\1\u0265\24\15\6\0\34\15\1\u0266\12\15" + "\6\0\25\15\1\u0267\21\15\6\0\16\15\1\u0268\30\15" + "\6\0\32\15\1\u0269\14\15\6\0\25\15\1\u026a\21\15" + "\6\0\24\15\1\u026b\22\15\6\0\25\15\1\u026c\21\15" + "\6\0\16\15\1\u026d\30\15\6\0\22\15\1\u026e\24\15" + "\6\0\26\15\1\u026f\20\15\6\0\7\15\1\u0270\37\15" + "\6\0\17\15\1\u0271\27\15\6\0\7\15\1\u0272\37\15" + "\6\0\16\15\1\u0273\30\15\6\0\37\15\1\u0274\7\15" + "\6\0\23\15\1\u0275\23\15\6\0\25\15\1\u0276\21\15" + "\6\0\21\15\1\u0277\25\15\6\0\16\15\1\u0278\30\15" + "\6\0\31\15\1\u0279\15\15\6\0\25\15\1\u027a\21\15" + "\6\0\25\15\1\u027b\21\15\6\0\34\15\1\u027c\12\15" + "\6\0\22\15\1\u027d\24\15\6\0\42\15\1\u027e\4\15" + "\6\0\21\15\1\u027f\25\15\6\0\35\15\1\u0280\11\15" + "\6\0\25\15\1\u0281\21\15\6\0\26\15\1\u0282\20\15" + "\6\0\34\15\1\u0283\12\15\6\0\27\15\1\u0284\17\15" + "\6\0\27\15\1\u0285\17\15\6\0\16\15\1\u0286\15\15" + "\1\u0287\12\15\6\0\21\15\1\u0288\25\15\6\0\17\15" + "\1\u0289\27\15\6\0\21\15\1\u028a\25\15\6\0\34\15" + "\1\u028b\12\15\6\0\21\15\1\u028c\25\15\6\0\25\15" + "\1\u028d\21\15\6\0\16\15\1\u028e\30\15\6\0\22\15" + "\1\u028f\24\15\6\0\32\15\1\u0290\14\15\6\0\36\15" + "\1\u0291\10\15\6\0\16\15\1\u0286\30\15\6\0\25\15" + "\1\u0292\21\15\6\0\33\15\1\u0293\13\15\6\0\16\15" + "\1\u0294\30\15\6\0\27\15\1\u0295\17\15\6\0\16\15" + "\1\u0296\30\15\6\0\32\15\1\u0297\14\15\6\0\31\15" + "\1\u0298\15\15\6\0\7\15\1\u0299\37\15\6\0\21\15" + "\1\u029a\25\15\6\0\33\15\1\u029b\13\15\6\0\16\15" + "\1\u029c\30\15\6\0\22\15\1\u029d\24\15\6\0\21\15" + "\1\u029e\25\15\6\0\36\15\1\u029f\10\15\6\0\27\15" + "\1\u02a0\17\15\6\0\34\15\1\u02a1\12\15\6\0\32\15" + "\1\u02a2\14\15\6\0\27\15\1\u02a3\17\15\6\0\32\15" + "\1\u02a4\14\15\6\0\22\15\1\u02a5\24\15\6\0\7\15" + "\1\u02a6\37\15\6\0\34\15\1\u02a7\12\15\6\0\31\15" + "\1\u02a8\15\15\6\0\24\15\1\u02a9\22\15\6\0\25\15" + "\1\u02aa\21\15\6\0\16\15\1\u02ab\30\15\6\0\25\15" + "\1\u02ac\21\15\6\0\34\15\1\u02ad\12\15\6\0\26\15" + "\1\u02ae\20\15\6\0\16\15\1\u02af\30\15\6\0\17\15" + "\1\u02b0\27\15\6\0\25\15\1\u02b1\21\15\6\0\26\15" + "\1\u02b2\20\15\6\0\27\15\1\u02b3\17\15\6\0\16\15" + "\1\u02b4\30\15\6\0\22\15\1\u02b5\24\15\6\0\7\15" + "\1\u02b6\37\15\6\0\32\15\1\u02b7\14\15\6\0\26\15" + "\1\u02b8\20\15\6\0\7\15\1\u02b9\37\15\6\0\22\15" + "\1\u02ba\24\15\6\0\25\15\1\u02bb\21\15\6\0\21\15" + "\1\u02bc\25\15\6\0\34\15\1\u02bd\12\15\6\0\25\15" + "\1\u02be\21\15\6\0\17\15\1\u02bf\27\15\6\0\21\15" + "\1\u02c0\25\15\6\0\26\15\1\u02c1\20\15\6\0\16\15" + "\1\u02c2\30\15\6\0\7\15\1\u02c3\37\15\6\0\26\15" + "\1\u02c4\20\15\6\0\22\15\1\u02c5\24\15\6\0\21\15" + "\1\u02c6\25\15\6\0\25\15\1\u02c7\21\15\6\0\21\15" + "\1\u02c8\25\15\2\0";
  /**
   * The transition table of the DFA
   */
  private static final int[] ZZ_TRANS = zzUnpackTrans();
  /* error codes */
  private static final int ZZ_UNKNOWN_ERROR = 0;
  private static final int ZZ_NO_MATCH = 1;
  private static final int ZZ_PUSHBACK_2BIG = 2;
  /* error messages for the codes above */
  private static final String ZZ_ERROR_MSG[] = {
      "Unkown internal scanner error",
      "Error: could not match input",
      "Error: pushback value was too large"
  };
  private static final String ZZ_ATTRIBUTE_PACKED_0 = "\7\0\1\1\1\11\1\1\1\11\4\1\2\11\24\1" + "\2\11\1\1\2\11\2\1\2\11\3\1\1\11\1\1" + "\3\0\67\1\1\0\3\1\1\0\1\11\105\1\1\0" + "\u020f\1";
  /**
   * ZZ_ATTRIBUTE[aState] contains the attributes of state <code>aState</code>
   */
  private static final int[] ZZ_ATTRIBUTE = zzUnpackAttribute();
  public static String scope_identifier = "";
  public static NumberFormat nf;
  /* user code: */ StringBuffer stringBuffer = new StringBuffer();
  /**
   * the input device
   */
  private java.io.Reader zzReader;
  /**
   * the current state of the DFA
   */
  private int zzState;
  /**
   * the current lexical state
   */
  private int zzLexicalState = YYINITIAL;
  /**
   * this buffer contains the current text to be matched and is the source of the yytext() string
   */
  private char zzBuffer[] = new char[ZZ_BUFFERSIZE];
  /**
   * the textposition at the last accepting state
   */
  private int zzMarkedPos;
  /**
   * the textposition at the last state to be included in yytext
   */
  private int zzPushbackPos;
  /**
   * the current text position in the buffer
   */
  private int zzCurrentPos;
  /**
   * startRead marks the beginning of the yytext() string in the buffer
   */
  private int zzStartRead;
  /**
   * endRead marks the last character in the buffer, that has been read from input
   */
  private int zzEndRead;
  /**
   * number of newlines encountered up to the start of the matched text
   */
  private int yyline;
  /**
   * the number of characters up to the start of the matched text
   */
  private int yychar;
  /**
   * the number of characters from the last newline up to the start of the matched text
   */
  private int yycolumn;
  /**
   * zzAtBOL == true <=> the scanner is currently at the beginning of a line
   */
  private boolean zzAtBOL = true;
  /**
   * zzAtEOF == true <=> the scanner is at the EOF
   */
  private boolean zzAtEOF;
  private Character[] stringSkipTrailing = {
      8,
      32
  };
  private Character[] stringSkipTrailingNewLines = {
      8,
      10,
      13,
      32
  };
  private Character[] stringStopAt = {
      8,
      10,
      13,
      32,
      40,
      41
  };
  private Character[] stringStopAtQuotes = {
      10,
      13,
      34
  };

  /**
   * Creates a new scanner There is also a java.io.InputStream version of this constructor.
   *
   * @param in the java.io.Reader to read input from.
   */
  SpecctraDsnStreamReader(java.io.Reader in)
  {
    this.zzReader = in;
  }

  /**
   * Creates a new scanner. There is also java.io.Reader version of this constructor.
   *
   * @param in the java.io.Inputstream to read input from.
   */
  SpecctraDsnStreamReader(java.io.InputStream in)
  {
    this(new java.io.InputStreamReader(in));
  }

  private static int[] zzUnpackAction()
  {
    int[] result = new int[712];
    int offset = 0;
    offset = zzUnpackAction(ZZ_ACTION_PACKED_0, offset, result);
    return result;
  }

  private static int zzUnpackAction(String packed, int offset, int[] result)
  {
    int i = 0; /* index in packed string  */
    int j = offset; /* index in unpacked array */
    int l = packed.length();
    while (i < l)
    {
      int count = packed.charAt(i++);
      int value = packed.charAt(i++);
      do
      {
        result[j++] = value;
      } while (--count > 0);
    }
    return j;
  }

  private static int[] zzUnpackRowMap()
  {
    int[] result = new int[712];
    int offset = 0;
    offset = zzUnpackRowMap(ZZ_ROWMAP_PACKED_0, offset, result);
    return result;
  }

  private static int zzUnpackRowMap(String packed, int offset, int[] result)
  {
    int i = 0; /* index in packed string  */
    int j = offset; /* index in unpacked array */
    int l = packed.length();
    while (i < l)
    {
      int high = packed.charAt(i++) << 16;
      result[j++] = high | packed.charAt(i++);
    }
    return j;
  }

  private static int[] zzUnpackTrans()
  {
    int[] result = new int[27450];
    int offset = 0;
    offset = zzUnpackTrans(ZZ_TRANS_PACKED_0, offset, result);
    return result;
  }

  private static int zzUnpackTrans(String packed, int offset, int[] result)
  {
    int i = 0; /* index in packed string  */
    int j = offset; /* index in unpacked array */
    int l = packed.length();
    while (i < l)
    {
      int count = packed.charAt(i++);
      int value = packed.charAt(i++);
      value--;
      do
      {
        result[j++] = value;
      } while (--count > 0);
    }
    return j;
  }

  private static int[] zzUnpackAttribute()
  {
    int[] result = new int[712];
    int offset = 0;
    offset = zzUnpackAttribute(ZZ_ATTRIBUTE_PACKED_0, offset, result);
    return result;
  }

  private static int zzUnpackAttribute(String packed, int offset, int[] result)
  {
    int i = 0; /* index in packed string  */
    int j = offset; /* index in unpacked array */
    int l = packed.length();
    while (i < l)
    {
      int count = packed.charAt(i++);
      int value = packed.charAt(i++);
      do
      {
        result[j++] = value;
      } while (--count > 0);
    }
    return j;
  }

  /**
   * Unpacks the compressed character translation table.
   *
   * @param packed the packed character translation table
   * @return the unpacked character translation table
   */
  private static char[] zzUnpackCMap(String packed)
  {
    char[] map = new char[0x10000];
    int i = 0; /* index in packed string  */
    int j = 0; /* index in unpacked array */
    while (i < 274)
    {
      int count = packed.charAt(i++);
      char value = packed.charAt(i++);
      do
      {
        map[j++] = value;
      } while (--count > 0);
    }
    return map;
  }

  /**
   * Refills the input buffer.
   *
   * @return <code>false</code>, iff there was new input.
   * @throws java.io.IOException if any I/O-Error occurs
   */
  private boolean zzRefill() throws java.io.IOException
  {

    /* first: make room (if you can) */
    if (zzStartRead > 0)
    {
      System.arraycopy(zzBuffer, zzStartRead, zzBuffer, 0, zzEndRead - zzStartRead);

      /* translate stored positions */
      zzEndRead -= zzStartRead;
      zzCurrentPos -= zzStartRead;
      zzMarkedPos -= zzStartRead;
      zzPushbackPos -= zzStartRead;
      zzStartRead = 0;
    }

    /* is the buffer big enough? */
    if (zzCurrentPos >= zzBuffer.length)
    {
      /* if not: blow it up */
      char newBuffer[] = new char[zzCurrentPos * 2];
      System.arraycopy(zzBuffer, 0, newBuffer, 0, zzBuffer.length);
      zzBuffer = newBuffer;
    }

    /* finally: fill the buffer with new input */
    int numRead = zzReader.read(zzBuffer, zzEndRead, zzBuffer.length - zzEndRead);

    if (numRead < 0)
    {
      return true;
    }
    else
    {
      zzEndRead += numRead;
      return false;
    }
  }

  /**
   * Closes the input stream.
   */
  public final void yyclose() throws java.io.IOException
  {
    zzAtEOF = true; /* indicate end of file */
    zzEndRead = zzStartRead; /* invalidate buffer    */

    if (zzReader != null)
    {
      zzReader.close();
    }
  }

  /**
   * Resets the scanner to read from a new input stream. Does not close the old reader.
   *
   * <p>All internal variables are reset, the old input stream <b>cannot</b> be reused (internal
   * buffer is discarded and lost). Lexical state is set to <tt>ZZ_INITIAL</tt>.
   *
   * @param reader the new input stream
   */
  public final void yyreset(java.io.Reader reader)
  {
    zzReader = reader;
    zzAtBOL = true;
    zzAtEOF = false;
    zzEndRead = zzStartRead = 0;
    zzCurrentPos = zzMarkedPos = zzPushbackPos = 0;
    yyline = yychar = yycolumn = 0;
    zzLexicalState = YYINITIAL;
  }

  /**
   * Returns the current lexical state.
   */
  public final int yystate()
  {
    return zzLexicalState;
  }

  /**
   * Enters a new lexical state
   *
   * @param newState the new lexical state
   */
  public final void yybegin(int newState)
  {
    zzLexicalState = newState;
  }

  /**
   * Returns the text matched by the current regular expression.
   */
  public final String yytext()
  {
    return new String(zzBuffer, zzStartRead, zzMarkedPos - zzStartRead);
  }

  /**
   * Returns the character at position <tt>pos</tt> from the matched text.
   *
   * <p>It is equivalent to yytext().charAt(pos), but faster
   *
   * @param pos the position of the character to fetch. A value from 0 to yylength()-1.
   * @return the character at position pos
   */
  public final char yycharat(int pos)
  {
    return zzBuffer[zzStartRead + pos];
  }

  /**
   * Returns the length of the matched text region.
   */
  public final int yylength()
  {
    return zzMarkedPos - zzStartRead;
  }

  /**
   * Reports an error that occured while scanning.
   *
   * <p>In a wellformed scanner (no or only correct usage of yypushback(int) and a match-all
   * fallback rule) this method will only be called with things that "Can't Possibly Happen". If
   * this method is called, something is seriously wrong (e.g. a JFlex bug producing a faulty
   * scanner etc.).
   *
   * <p>Usual syntax/scanner level error handling should be done in error fallback rules.
   *
   * @param errorCode the code of the errormessage to display
   */
  private void zzScanError(int errorCode)
  {
    String message;
    try
    {
      message = ZZ_ERROR_MSG[errorCode];
    } catch (ArrayIndexOutOfBoundsException e)
    {
      message = ZZ_ERROR_MSG[ZZ_UNKNOWN_ERROR];
    }

    throw new Error(message);
  }

  /**
   * Pushes the specified amount of characters back into the input stream.
   *
   * <p>They will be read again by then next call of the scanning method
   *
   * @param number the number of characters to be read again. This number must not be greater than
   *               yylength()!
   */
  public void yypushback(int number)
  {
    if (number > yylength())
    {
      zzScanError(ZZ_PUSHBACK_2BIG);
    }

    zzMarkedPos -= number;
  }

  @Override
  public String get_scope_identifier()
  {
    return scope_identifier;
  }

  @Override
  public void set_scope_identifier(String identifier)
  {
    scope_identifier = identifier;
  }

  /**
   * Resumes scanning until the next regular expression is matched, the end of input is encountered
   * or an I/O-Error occurs.
   *
   * @return the next token
   * @throws java.io.IOException if any I/O-Error occurs
   */
  public Object next_token() throws java.io.IOException
  {
    int zzInput;
    int zzAction;

    // cached fields:
    int zzCurrentPosL;
    int zzMarkedPosL;
    int zzEndReadL = zzEndRead;
    char[] zzBufferL = zzBuffer;
    char[] zzCMapL = ZZ_CMAP;

    int[] zzTransL = ZZ_TRANS;
    int[] zzRowMapL = ZZ_ROWMAP;
    int[] zzAttrL = ZZ_ATTRIBUTE;

    while (true)
    {
      zzMarkedPosL = zzMarkedPos;

      zzAction = -1;

      zzCurrentPosL = zzCurrentPos = zzStartRead = zzMarkedPosL;

      zzState = zzLexicalState;

      zzForAction:
      {
        while (true)
        {

          if (zzCurrentPosL < zzEndReadL)
          {
            zzInput = zzBufferL[zzCurrentPosL++];
          }
          else if (zzAtEOF)
          {
            zzInput = YYEOF;
            break zzForAction;
          }
          else
          {
            // store back cached positions
            zzCurrentPos = zzCurrentPosL;
            zzMarkedPos = zzMarkedPosL;
            boolean eof = zzRefill();
            // get translated positions and possibly new buffer
            zzCurrentPosL = zzCurrentPos;
            zzMarkedPosL = zzMarkedPos;
            zzBufferL = zzBuffer;
            zzEndReadL = zzEndRead;
            if (eof)
            {
              zzInput = YYEOF;
              break zzForAction;
            }
            else
            {
              zzInput = zzBufferL[zzCurrentPosL++];
            }
          }
          int zzNext = zzTransL[zzRowMapL[zzState] + zzCMapL[zzInput]];
          if (zzNext == -1)
          {
            break zzForAction;
          }
          zzState = zzNext;

          int zzAttributes = zzAttrL[zzState];
          if ((zzAttributes & 1) == 1)
          {
            zzAction = zzState;
            zzMarkedPosL = zzCurrentPosL;
            if ((zzAttributes & 8) == 8)
            {
              break zzForAction;
            }
          }
        }
      }

      // store back cached position
      zzMarkedPos = zzMarkedPosL;

      switch (zzAction < 0 ? zzAction : ZZ_ACTION[zzAction])
      {
        case 18:
        {
          yybegin(NAME);
          return Keyword.VIA;
        }
        case 121:
          break;
        case 24:
        {
          return Keyword.BACK;
        }
        case 122:
          break;
        case 31:
        {
          yybegin(LAYER_NAME);
          return Keyword.POLYGON_PATH;
        }
        case 123:
          break;
        case 71:
        {
          return Keyword.NETWORK_SCOPE;
        }
        case 124:
          break;
        case 56:
        {
          return Keyword.ROUTES;
        }
        case 125:
          break;
        case 93:
        {
          return Keyword.FLIP_STYLE;
        }
        case 126:
          break;
        case 108:
        {
          return Keyword.PLACE_CONTROL;
        }
        case 127:
          break;
        case 32:
        {
          yybegin(LAYER_NAME);
          return Keyword.POLYGON;
        }
        case 128:
          break;
        case 78:
        {
          yybegin(NAME);
          return Keyword.PADSTACK;
        }
        case 129:
          break;
        case 110:
        {
          yybegin(NAME);
          return Keyword.CLEARANCE_CLASS;
        }
        case 130:
          break;
        case 115:
        {
          return Keyword.AUTOROUTE_SETTINGS;
        }
        case 131:
          break;
        case 13:
        {
          yybegin(YYINITIAL);
          return Keyword.OPEN_BRACKET;
        }
        case 132:
          break;
        case 105:
        {
          return Keyword.START_PASS_NO;
        }
        case 133:
          break;
        case 23:
        {
          yybegin(YYINITIAL);
          return Keyword.PCB_SCOPE;
        }
        case 134:
          break;
        case 34:
        {
          yybegin(LAYER_NAME);
          return Keyword.RECTANGLE;
        }
        case 135:
          break;
        case 82:
        {
          return Keyword.STRUCTURE_SCOPE;
        }
        case 136:
          break;
        case 27:
        {
          yybegin(NAME);
          return Keyword.COMPONENT_SCOPE;
        }
        case 137:
          break;
        case 43:
        {
          yybegin(NAME);
          return Keyword.IMAGE;
        }
        case 138:
          break;
        case 112:
        {
          return Keyword.FORTYFIVE_DEGREE;
        }
        case 139:
          break;
        case 60:
        {
          return Keyword.WINDOW;
        }
        case 140:
          break;
        case 76:
        {
          return Keyword.VERTICAL;
        }
        case 141:
          break;
        case 20:
        {
          return Keyword.PCB_SCOPE;
        }
        case 142:
          break;
        case 37:
        {
          return Keyword.SPARE;
        }
        case 143:
          break;
        case 21:
        {
          return Keyword.PIN;
        }
        case 144:
          break;
        case 35:
        {
          return Keyword.RULE;
        }
        case 145:
          break;
        case 29:
        {
          return Keyword.VIAS;
        }
        case 146:
          break;
        case 16:
        {
          return Keyword.ON;
        }
        case 147:
          break;
        case 63:
        {
          return Keyword.SESSION;
        }
        case 148:
          break;
        case 74:
        {
          return Keyword.BOUNDARY;
        }
        case 149:
          break;
        case 88:
        {
          return Keyword.SNAP_ANGLE;
        }
        case 150:
          break;
        case 113:
        {
          return Keyword.WRITE_RESOLUTION;
        }
        case 151:
          break;
        case 54:
        {
          return Keyword.NORMAL;
        }
        case 152:
          break;
        case 8:
        {
          return Keyword.OPEN_BRACKET;
        }
        case 153:
          break;
        case 22:
        {
          return Keyword.FIX;
        }
        case 154:
          break;
        case 89:
        {
          yybegin(NAME);
          return Keyword.LAYER_RULE;
        }
        case 155:
          break;
        case 86:
        {
          return Keyword.POSTROUTE;
        }
        case 156:
          break;
        case 14:
        {
          yybegin(YYINITIAL);
          return Keyword.CLOSED_BRACKET;
        }
        case 157:
          break;
        case 9:
        {
          return Keyword.CLOSED_BRACKET;
        }
        case 158:
          break;
        case 90:
        {
          return Keyword.VIA_AT_SMD;
        }
        case 159:
          break;
        case 28:
        {
          yybegin(LAYER_NAME);
          return Keyword.CIRCLE;
        }
        case 160:
          break;
        case 50:
        {
          return Keyword.ATTACH;
        }
        case 161:
          break;
        case 92:
        {
          return Keyword.RESOLUTION_SCOPE;
        }
        case 162:
          break;
        case 77:
        {
          return Keyword.VIA_RULE;
        }
        case 163:
          break;
        case 70:
        {
          return Keyword.CIRCUIT;
        }
        case 164:
          break;
        case 87:
        {
          return Keyword.PLACEMENT_SCOPE;
        }
        case 165:
          break;
        case 36:
        {
          yybegin(NAME);
          return Keyword.WIRE;
        }
        case 166:
          break;
        case 116:
        {
          return Keyword.PREFERRED_DIRECTION;
        }
        case 167:
          break;
        case 40:
        {
          yybegin(NAME);
          return Keyword.LAYER;
        }
        case 168:
          break;
        case 69:
        {
          return Keyword.CLASSES;
        }
        case 169:
          break;
        case 61:
        {
          return Keyword.WIRING_SCOPE;
        }
        case 170:
          break;
        case 103:
        {
          yybegin(NAME);
          return Keyword.HOST_VERSION;
        }
        case 171:
          break;
        case 4:
        {
          return yytext();
        }
        case 172:
          break;
        case 73:
        {
          return Keyword.ABSOLUTE;
        }
        case 173:
          break;
        case 58:
        {
          return Keyword.FANOUT;
        }
        case 174:
          break;
        case 79:
        {
          return Keyword.POSITION;
        }
        case 175:
          break;
        case 47:
        {
          return Keyword.RULES;
        }
        case 176:
          break;
        case 57:
        {
          return Keyword.ROTATE;
        }
        case 177:
          break;
        case 7:
        {
          stringBuffer.setLength(0);
          yybegin(STRING2);
        }
        case 178:
          break;
        case 84:
        {
          yybegin(NAME);
          return Keyword.USE_LAYER;
        }
        case 179:
          break;
        case 80:
        {
          yybegin(NAME);
          return Keyword.HOST_CAD;
        }
        case 180:
          break;
        case 64:
        {
          return Keyword.OUTLINE;
        }
        case 181:
          break;
        case 45:
        {
          yybegin(NAME);
          return Keyword.PLACE;
        }
        case 182:
          break;
        case 6:
        {
          // we are at the beginning of a text string that is surrounded by double quotes
          stringBuffer.setLength(0);
          yybegin(STRING1);
        }
        case 183:
          break;
        case 99:
        {
          yybegin(IGNORE_QUOTE);
          return Keyword.STRING_QUOTE;
        }
        case 184:
          break;
        case 39:
        {
          return Keyword.ORDER;
        }
        case 185:
          break;
        case 17:
        {
          return Keyword.OFF;
        }
        case 186:
          break;
        case 81:
        {
          return Keyword.AUTOROUTE;
        }
        case 187:
          break;
        case 52:
        {
          return Keyword.SIGNAL;
        }
        case 188:
          break;
        case 107:
        {
          yybegin(LAYER_NAME);
          return Keyword.POLYLINE_PATH;
        }
        case 189:
          break;
        case 68:
        {
          return Keyword.CONTROL;
        }
        case 190:
          break;
        case 46:
        {
          yybegin(NAME);
          return Keyword.PLANE_SCOPE;
        }
        case 191:
          break;
        case 1:
        {
          yybegin(YYINITIAL);
          return yytext();
        }
        case 192:
          break;
        case 100:
        {
          yybegin(NAME);
          return Keyword.LOGICAL_PART;
        }
        case 193:
          break;
        case 83:
        {
          return Keyword.LOCK_TYPE;
        }
        case 194:
          break;
        case 109:
        {
          yybegin(NAME);
          return Keyword.PLACE_KEEPOUT;
        }
        case 195:
          break;
        case 49:
        {
          return Keyword.WIDTH;
        }
        case 196:
          break;
        case 106:
        {
          return Keyword.NINETY_DEGREE;
        }
        case 197:
          break;
        case 67:
        {
          yybegin(NAME);
          return Keyword.USE_NET;
        }
        case 198:
          break;
        case 118:
        {
          return Keyword.GENERATED_BY_FREEROUTING;
        }
        case 199:
          break;
        case 101:
        {
          return Keyword.PART_LIBRARY_SCOPE;
        }
        case 200:
          break;
        case 85:
        {
          return Keyword.VIA_COSTS;
        }
        case 201:
          break;
        case 102:
        {
          return Keyword.ROTATE_FIRST;
        }
        case 202:
          break;
        case 75:
        {
          return Keyword.CONSTANT;
        }
        case 203:
          break;
        case 12:
        {
          stringBuffer.append('\\');
        }
        case 204:
          break;
        case 15:
        {
          // we need to parse the text value as double
          return Double.valueOf(yytext());
        }
        case 205:
          break;
        case 42:
        {
          yybegin(NAME);
          return Keyword.CLASS;
        }
        case 206:
          break;
        case 91:
        {
          return Keyword.PULL_TIGHT;
        }
        case 207:
          break;
        case 30:
        {
          return Keyword.NONE;
        }
        case 208:
          break;
        case 120:
        {
          return Keyword.AGAINST_PREFERRED_DIRECTION_TRACE_COSTS;
        }
        case 209:
          break;
        case 114:
        {
          return Keyword.START_RIPUP_COSTS;
        }
        case 210:
          break;
        case 53:
        {
          return Keyword.LENGTH;
        }
        case 211:
          break;
        case 38:
        {
          return Keyword.SHAPE;
        }
        case 212:
          break;
        case 59:
        {
          return Keyword.FROMTO;
        }
        case 213:
          break;
        case 44:
        {
          return Keyword.POWER;
        }
        case 214:
          break;
        case 94:
        {
          return Keyword.HORIZONTAL;
        }
        case 215:
          break;
        case 26:
        {
          return Keyword.TYPE;
        }
        case 216:
          break;
        case 51:
        {
          return Keyword.ACTIVE;
        }
        case 217:
          break;
        case 48:
        {
          return Keyword.FRONT;
        }
        case 218:
          break;
        case 11:
        {
          yybegin(YYINITIAL);
          return stringBuffer.toString();
        }
        case 219:
          break;
        case 19:
        {
          yybegin(NAME);
          return Keyword.NET;
        }
        case 220:
          break;
        case 96:
        {
          return Keyword.CLASS_CLASS;
        }
        case 221:
          break;
        case 66:
        {
          yybegin(NAME);
          return Keyword.USE_VIA;
        }
        case 222:
          break;
        case 65:
        {
          return Keyword.LIBRARY_SCOPE;
        }
        case 223:
          break;
        case 3:
        {
          /* ignore */
        }
        case 224:
          break;
        case 117:
        {
          yybegin(NAME);
          return Keyword.LOGICAL_PART_MAPPING;
        }
        case 225:
          break;
        case 111:
        {
          return Keyword.PLANE_VIA_COSTS;
        }
        case 226:
          break;
        case 72:
        {
          yybegin(NAME);
          return Keyword.KEEPOUT;
        }
        case 227:
          break;
        case 98:
        {
          return Keyword.NETWORK_OUT;
        }
        case 228:
          break;
        case 55:
        {
          return Keyword.PARSER_SCOPE;
        }
        case 229:
          break;
        case 10:
        {
          // we are at the end of a text string that is surrounded by double quotes
          stringBuffer.append(yytext());
        }
        case 230:
          break;
        case 2:
        {
          FRLogger.warn("Non-ansi character '" + yytext() + "' found at position " + zzCurrentPos + " just after '" + stringBuffer + "'.");
          break;
        }
        case 231:
          break;
        case 95:
        {
          return Keyword.SHOVE_FIXED;
        }
        case 232:
          break;
        case 104:
        {
          return Keyword.KEEPOUT;
        }
        case 233:
          break;
        case 33:
        {
          return Keyword.PINS;
        }
        case 234:
          break;
        case 119:
        {
          return Keyword.PREFERRED_DIRECTION_TRACE_COSTS;
        }
        case 235:
          break;
        case 5:
        {
          return Integer.valueOf(yytext());
        }
        case 236:
          break;
        case 25:
        {
          return Keyword.SIDE;
        }
        case 237:
          break;
        case 41:
        {
          return Keyword.CLEARANCE;
        }
        case 238:
          break;
        case 97:
        {
          yybegin(NAME);
          return Keyword.VIA_KEEPOUT;
        }
        case 239:
          break;
        case 62:
        {
          yybegin(YYINITIAL);
          return Keyword.SIGNAL;
        }
        case 240:
          break;
        default:
          if (zzInput == YYEOF && zzStartRead == zzCurrentPos)
          {
            zzAtEOF = true;
            return null;
          }
          else
          {
            zzScanError(ZZ_NO_MATCH);
          }
      }
    }
  }

  public String next_string()
  {
    return next_string(false, ' ');
  }

  public String next_string(boolean ignoreNewline)
  {
    return next_string(ignoreNewline, ' ');
  }

  public String next_string(boolean ignoreNewline, char leading)
  {
    stringBuffer.setLength(0);
    int i = 0;

    List<Character> skipLeading = null;

    if (ignoreNewline)
    {
      // let's ignore the leading spaces, tabs and new lines
      skipLeading = new ArrayList<>(Arrays.asList(stringSkipTrailingNewLines));
    }
    else
    {
      // let's ignore the leading spaces, tabs
      skipLeading = new ArrayList<>(Arrays.asList(stringSkipTrailing));
    }
    skipLeading.add(Character.valueOf(leading));

    while ((zzMarkedPos + i < zzBuffer.length) && (skipLeading.contains(zzBuffer[zzMarkedPos + i])))
    {
      i++;
    }

    boolean skipLastChar = false;
    List<Character> stopAt = null;
    if (zzBuffer[zzMarkedPos + i] == 34)
    {
      stopAt = new ArrayList<>(Arrays.asList(stringStopAtQuotes));
      i++;
      skipLastChar = true;
    }
    else
    {
      stopAt = new ArrayList<>(Arrays.asList(stringStopAt));
      stopAt.add(Character.valueOf(leading));
    }

    // read the actual string until we have a space/tab/new line
    while ((zzMarkedPos + i < zzBuffer.length) && (!stopAt.contains(zzBuffer[zzMarkedPos + i])))
    {
      stringBuffer.append(zzBuffer[zzMarkedPos + i]);
      i++;
    }

    if (skipLastChar)
    {
      i++;
    }

    if (i > 0)
    {
      zzStartRead += i - 1;
      zzCurrentPos += i - 1;
      zzMarkedPos += i;
      zzLexicalState = YYINITIAL;

      if ((zzBuffer[zzMarkedPos - 1] == 40) || (zzBuffer[zzMarkedPos - 1] == 41))
      {
        // if the string ended with brackets, we need to step back one character, because we need
        // that bracket as the next token
        zzStartRead--;
        zzCurrentPos--;
        zzMarkedPos--;
      }
    }

    return stringBuffer.toString();
  }

  public String[] next_string_list()
  {
    return next_string_list(' ');
  }

  public String[] next_string_list(char separator)
  {
    java.util.Collection<String> result = new java.util.LinkedList<String>();

    // Every string list must have at least one item, but the first item can be empty. In this case we ignore that and continue reading the next item.
    // This is extra step is also needed to handle the bug KiCad 8 introduced in the netlist definition, when it started the list of nets with a "" string.
    String first_string = next_string(true, separator);
    if (first_string.length() > 0)
    {
      result.add(first_string);
    }

    for (; ; )
    {
      String next_string = next_string(true, separator);
      if (next_string.length() == 0)
      {
        break;
      }

      result.add(next_string);
    }

    zzStartRead = zzMarkedPos - 1;
    zzCurrentPos = zzMarkedPos - 1;

    return result.toArray(new String[result.size()]);
  }

  public Double next_double()
  {
    String s = next_string();

    if (nf == null)
    {
      nf = NumberFormat.getInstance(Locale.US);
    }
    try
    {
      return nf.parse(s).doubleValue();
    } catch (ParseException e)
    {
      return null;
    }
  }

  public Boolean next_closing_bracket()
  {
    Object next_token;
    try
    {
      next_token = next_token();
    } catch (java.io.IOException e)
    {
      FRLogger.error("Network.read_net_pins: IO error scanning file", e);
      return false;
    }
    if (next_token == null)
    {
      FRLogger.warn("Network.read_net_pins: unexpected end of file");
      return false;
    }
    if (next_token != Keyword.CLOSED_BRACKET)
    {
      // not end of scope
      FRLogger.warn("Network.read_net_pins: expected closed bracket is missing");
      return false;
    }

    return true;
  }
}