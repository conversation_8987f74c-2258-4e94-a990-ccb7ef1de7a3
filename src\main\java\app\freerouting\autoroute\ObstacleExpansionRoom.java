package app.freerouting.autoroute;

import app.freerouting.board.Item;
import app.freerouting.board.PolylineTrace;
import app.freerouting.board.SearchTreeObject;
import app.freerouting.board.ShapeSearchTree;
import app.freerouting.boardgraphics.GraphicsContext;
import app.freerouting.geometry.planar.TileShape;

import java.awt.Color;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * Expansion Room used for pushing and ripping obstacles in the autoroute algorithm.
 */
public class ObstacleExpansionRoom implements CompleteExpansionRoom
{

  private final Item item;
  private final int index_in_item;
  private final TileShape shape;
  /**
   * The list of doors to neighbour expansion rooms
   */
  private List<ExpansionDoor> doors;
  private boolean doors_calculated = false;

  /**
   * Creates a new instance of ObstacleExpansionRoom
   */
  ObstacleExpansionRoom(Item p_item, int p_index_in_item, ShapeSearchTree p_shape_tree)
  {
    this.item = p_item;
    this.index_in_item = p_index_in_item;
    this.shape = p_item.get_tree_shape(p_shape_tree, p_index_in_item);
    this.doors = new LinkedList<>();
  }

  public int get_index_in_item()
  {
    return this.index_in_item;
  }

  @Override
  public int get_layer()
  {
    return this.item.shape_layer(this.index_in_item);
  }

  @Override
  public TileShape get_shape()
  {
    return this.shape;
  }

  /**
   * Checks, if this room has already a 1-dimensional door to p_other
   */
  @Override
  public boolean door_exists(ExpansionRoom p_other)
  {
    if (doors != null)
    {
      for (ExpansionDoor curr_door : this.doors)
      {
        if (curr_door.first_room == p_other || curr_door.second_room == p_other)
        {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * Adds a door to the door list of this room.
   */
  @Override
  public void add_door(ExpansionDoor p_door)
  {
    this.doors.add(p_door);
  }

  /**
   * Creates a 2-dim door with the other obstacle room, if that is useful for the autoroute
   * algorithm. It is assumed that this room and p_other have a 2-dimensional overlap. Returns
   * false, if no door was created.
   */
  public boolean create_overlap_door(ObstacleExpansionRoom p_other)
  {
    if (this.door_exists(p_other))
    {
      return false;
    }
    if (!(this.item.is_routable() && p_other.item.is_routable()))
    {
      return false;
    }
    if (!this.item.shares_net(p_other.item))
    {
      return false;
    }
    if (this.item == p_other.item)
    {
      if (!(this.item instanceof PolylineTrace))
      {
        return false;
      }
      // create only doors between consecutive trace segments
      if (this.index_in_item != p_other.index_in_item + 1 && this.index_in_item != p_other.index_in_item - 1)
      {
        return false;
      }
    }
    ExpansionDoor new_door = new ExpansionDoor(this, p_other, 2);
    this.add_door(new_door);
    p_other.add_door(new_door);
    return true;
  }

  /**
   * Returns the list of doors of this room to neighbour expansion rooms
   */
  @Override
  public List<ExpansionDoor> get_doors()
  {
    return this.doors;
  }

  /**
   * Removes all doors from this room.
   */
  @Override
  public void clear_doors()
  {
    this.doors = new LinkedList<>();
  }

  @Override
  public void reset_doors()
  {
    for (ExpandableObject curr_door : this.doors)
    {
      curr_door.reset();
    }
  }

  @Override
  public Collection<TargetItemExpansionDoor> get_target_doors()
  {
    return new LinkedList<>();
  }

  public Item get_item()
  {
    return this.item;
  }

  @Override
  public SearchTreeObject get_object()
  {
    return this.item;
  }

  @Override
  public boolean remove_door(ExpandableObject p_door)
  {
    return this.doors.remove(p_door);
  }

  /**
   * Returns, if all doors to the neighbour rooms are calculated.
   */
  boolean all_doors_calculated()
  {
    return this.doors_calculated;
  }

  void set_doors_calculated(boolean p_value)
  {
    this.doors_calculated = p_value;
  }

  /**
   * Draws the shape of this room.
   * 绘制障碍物房间形状 - 增强可视化效果，包含清晰边界和房间编号
   */
  @Override
  public void draw(Graphics p_graphics, GraphicsContext p_graphics_context, double p_intensity)
  {
    // 使用明亮的红色来表示障碍物房间，提高可见性
    Color fill_color = new Color(255, 0, 0, 60); // 明亮红色，更透明以突出边界
    Color border_color = new Color(180, 0, 0, 255); // 深红色边框，完全不透明
    Color text_color = new Color(255, 255, 255, 255); // 白色文字，完全不透明
    Color text_bg_color = new Color(150, 0, 0, 200); // 深红色文字背景

    double layer_visibility = p_graphics_context.get_layer_visibility(this.get_layer());

    // 填充障碍物区域
    p_graphics_context.fill_area(this.get_shape(), p_graphics, fill_color,
                                Math.max(0.3, p_intensity * layer_visibility));

    // 绘制多层边界以增强可见性
    // 外层边界 - 更粗更明显
    p_graphics_context.draw_boundary(this.get_shape(), 4.0, border_color, p_graphics,
                                   Math.max(0.9, layer_visibility));

    // 内层边界 - 细线增强对比
    Color inner_border = new Color(220, 0, 0, 180);
    p_graphics_context.draw_boundary(this.get_shape(), 1.0, inner_border, p_graphics,
                                   Math.max(0.7, layer_visibility));

    // 绘制障碍物编号
    draw_obstacle_number(p_graphics, p_graphics_context, text_color, text_bg_color, p_intensity);
  }

  /**
   * 绘制障碍物编号 - 增强版本，带背景和更好的可见性
   */
  private void draw_obstacle_number(Graphics p_graphics, GraphicsContext p_graphics_context,
                                   Color p_text_color, Color p_bg_color, double p_intensity)
  {
    try
    {
      // 获取障碍物的中心点
      app.freerouting.geometry.planar.IntBox bounding_box = this.get_shape().bounding_box();
      if (bounding_box == null) return;

      // 检查障碍物大小，只在足够大的障碍物中显示编号
      int obstacle_width = bounding_box.ur.x - bounding_box.ll.x;
      int obstacle_height = bounding_box.ur.y - bounding_box.ll.y;
      if (obstacle_width < 800 || obstacle_height < 800) return; // 小于0.8mm的障碍物不显示编号

      int center_x = (bounding_box.ll.x + bounding_box.ur.x) / 2;
      int center_y = (bounding_box.ll.y + bounding_box.ur.y) / 2;

      // 转换为屏幕坐标
      java.awt.geom.Point2D screen_point = p_graphics_context.coordinate_transform.board_to_screen(
          new app.freerouting.geometry.planar.FloatPoint(center_x, center_y));

      // 设置字体和颜色
      Graphics2D g2d = (Graphics2D) p_graphics;
      g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING,
                          java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
      g2d.setFont(new java.awt.Font("Arial", java.awt.Font.BOLD, 12));

      // 绘制障碍物编号文字
      String obstacle_text = "O" + this.item.get_id_no();
      java.awt.FontMetrics fm = g2d.getFontMetrics();
      int text_width = fm.stringWidth(obstacle_text);
      int text_height = fm.getHeight();

      int text_x = (int)(screen_point.getX() - text_width/2);
      int text_y = (int)(screen_point.getY() + text_height/4);

      // 绘制文字背景（圆角矩形）
      int padding = 3;
      int bg_x = text_x - padding;
      int bg_y = text_y - text_height + padding;
      int bg_width = text_width + 2 * padding;
      int bg_height = text_height;

      g2d.setColor(p_bg_color);
      g2d.fillRoundRect(bg_x, bg_y, bg_width, bg_height, 5, 5);

      // 绘制文字边框
      g2d.setColor(new Color(100, 0, 0, 255));
      g2d.setStroke(new java.awt.BasicStroke(1.0f));
      g2d.drawRoundRect(bg_x, bg_y, bg_width, bg_height, 5, 5);

      // 绘制文字
      g2d.setColor(p_text_color);
      g2d.drawString(obstacle_text, text_x, text_y);
    }
    catch (Exception e)
    {
      // 忽略绘制错误
    }
  }
}