(pcb C:\freerouting_test\ultra_simple.dsn
  (parser
    (string_quote ")
    (space_in_quoted_tokens on)
    (host_cad "KiCad's Pcbnew")
    (host_version "8.0.1")
  )
  (resolution um 10)
  (unit um)
  (structure
    (layer F.Cu
      (type signal)
      (property
        (index 0)
      )
    )
    (layer B.Cu
      (type signal)
      (property
        (index 1)
      )
    )
    (boundary
      (path pcb 0  25000 -18000  0 -18000  0 0  25000 0  25000 -18000)
    )
    (via "Via[0-1]_800:400_um")
    (rule
      (width 500)
      (clearance 300)
      (clearance 300 (type default_smd))
      (clearance 50 (type smd_smd))
    )
  )
  (placement
    (component TestPoint:TestPoint_Pad_1.5x1.5mm
      (place TP1 4000.000000 -9000.000000 front 0.000000 (PN TestPoint))
      (place TP2 21000.000000 -9000.000000 front 0.000000 (PN TestPoint))
    )
  )
  (library
    (image TestPoint:TestPoint_Pad_1.5x1.5mm
      (outline (path signal 120  -950 950  950 950))
      (outline (path signal 120  -950 -950  -950 950))
      (outline (path signal 120  950 950  950 -950))
      (outline (path signal 120  950 -950  -950 -950))
      (pin Round[T]Pad_1500_um 1 0 0)
    )
    (padstack Round[T]Pad_1500_um
      (shape (circle F.Cu 750))
      (attach off)
    )
    (padstack "Via[0-1]_800:400_um"
      (shape (circle F.Cu 800))
      (shape (circle B.Cu 800))
      (attach off)
    )
  )
  (network
    (net SIGNAL
      (pins TP1-1 TP2-1)
    )
    (class default
      SIGNAL
      (circuit
        (use_via "Via[0-1]_800:400_um")
      )
      (rule
        (width 500)
        (clearance 300)
      )
    )
  )
  (wiring
  )
)
