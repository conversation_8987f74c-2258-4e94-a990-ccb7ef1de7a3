package app.freerouting.autoroute;

import app.freerouting.board.Connectable;
import app.freerouting.board.Item;
import app.freerouting.board.SearchTreeObject;
import app.freerouting.board.ShapeSearchTree;
import app.freerouting.boardgraphics.GraphicsContext;
import app.freerouting.datastructures.ShapeTree;
import app.freerouting.geometry.planar.TileShape;
import app.freerouting.logger.FRLogger;

import java.awt.*;
import java.util.Collection;
import java.util.LinkedList;

/**
 * An expansion room, whose shape is completely calculated, so that it can be stored in a shape
 * tree.
 */
public class CompleteFreeSpaceExpansionRoom extends FreeSpaceExpansionRoom implements CompleteExpansionRoom, SearchTreeObject
{

  // ** identification number for implementing the Comparable interface */
  private final int id_no;
  /**
   * The array of entries in the SearchTree. Consists of just one element
   */
  private ShapeTree.Leaf[] tree_entries;
  /**
   * The list of doors to items of the own net
   */
  private Collection<TargetItemExpansionDoor> target_doors;
  private boolean room_is_net_dependent = false;

  /**
   * Creates a new instance of CompleteFreeSpaceExpansionRoom
   */
  public CompleteFreeSpaceExpansionRoom(TileShape p_shape, int p_layer, int p_id_no)
  {
    super(p_shape, p_layer);
    target_doors = new LinkedList<>();
    id_no = p_id_no;
  }

  @Override
  public void set_search_tree_entries(ShapeTree.Leaf[] p_entries, ShapeTree p_tree)
  {
    tree_entries = p_entries;
  }

  @Override
  public int compareTo(Object p_other)
  {
    int result;
    if (p_other instanceof FreeSpaceExpansionRoom)
    {
      result = ((CompleteFreeSpaceExpansionRoom) p_other).id_no - this.id_no;
    }
    else
    {
      result = -1;
    }
    return result;
  }

  /**
   * Removes the tree entries of this room from p_shape_tree.
   */
  public void remove_from_tree(ShapeTree p_shape_tree)
  {
    p_shape_tree.remove(this.tree_entries);
  }

  @Override
  public int tree_shape_count(ShapeTree p_shape_tree)
  {
    return 1;
  }

  @Override
  public TileShape get_tree_shape(ShapeTree p_shape_tree, int p_index)
  {
    return this.get_shape();
  }

  @Override
  public int shape_layer(int p_index)
  {
    return this.get_layer();
  }

  @Override
  public boolean is_obstacle(int p_net_no)
  {
    return true;
  }

  @Override
  public boolean is_trace_obstacle(int p_net_no)
  {
    return true;
  }

  /**
   * Will be called, when the room overlaps with net dependent objects.
   */
  public void set_net_dependent()
  {
    this.room_is_net_dependent = true;
  }

  /**
   * Returns, if the room overlaps with net dependent objects. In this case it cannot be retained,
   * when the net number changes in autorouting.
   */
  public boolean is_net_dependent()
  {
    return this.room_is_net_dependent;
  }

  /**
   * Returns the list doors to target items of this room
   */
  @Override
  public Collection<TargetItemExpansionDoor> get_target_doors()
  {
    return this.target_doors;
  }

  /**
   * Adds p_door to the list of target doors of this room.
   */
  public void add_target_door(TargetItemExpansionDoor p_door)
  {
    this.target_doors.add(p_door);
  }

  @Override
  public boolean remove_door(ExpandableObject p_door)
  {
    boolean result;
    if (p_door instanceof TargetItemExpansionDoor)
    {
      result = this.target_doors.remove(p_door);
    }
    else
    {
      result = super.remove_door(p_door);
    }
    return result;
  }

  @Override
  public SearchTreeObject get_object()
  {
    return this;
  }

  /**
   * Calculates the doors to the start and destination items of the autoroute algorithm.
   */
  public void calculate_target_doors(ShapeTree.TreeEntry p_own_net_object, int p_net_no, ShapeSearchTree p_autoroute_search_tree)
  {
    this.set_net_dependent();

    if (p_own_net_object.object instanceof Connectable curr_object)
    {
      if (curr_object.contains_net(p_net_no))
      {
        TileShape curr_connection_shape = curr_object.get_trace_connection_shape(p_autoroute_search_tree, p_own_net_object.shape_index_in_object);
        if (curr_connection_shape != null && this.get_shape().intersects(curr_connection_shape))
        {
          Item curr_item = (Item) curr_object;
          TargetItemExpansionDoor new_target_door = new TargetItemExpansionDoor(curr_item, p_own_net_object.shape_index_in_object, this, p_autoroute_search_tree);
          this.add_target_door(new_target_door);
        }
      }
    }
  }

  /**
   * Draws the shape of this room.
   * 绘制房间形状 - 增强可视化效果，包含清晰边界和房间编号
   */
  @Override
  public void draw(Graphics p_graphics, GraphicsContext p_graphics_context, double p_intensity)
  {
    // 使用明亮的绿色来表示自由空间房间，提高可见性
    Color fill_color = new Color(0, 255, 0, 80); // 明亮绿色，更透明以突出边界
    Color border_color = new Color(0, 150, 0, 255); // 深绿色边框，完全不透明
    Color text_color = new Color(255, 255, 255, 255); // 白色文字，完全不透明
    Color text_bg_color = new Color(0, 120, 0, 200); // 深绿色文字背景

    double layer_visibility = p_graphics_context.get_layer_visibility(this.get_layer());

    // 填充房间区域
    p_graphics_context.fill_area(this.get_shape(), p_graphics, fill_color,
                                Math.max(0.4, p_intensity * layer_visibility));

    // 绘制多层边界以增强可见性
    // 外层边界 - 更粗更明显
    p_graphics_context.draw_boundary(this.get_shape(), 4.0, border_color, p_graphics,
                                   Math.max(0.9, layer_visibility));

    // 内层边界 - 细线增强对比
    Color inner_border = new Color(0, 200, 0, 180);
    p_graphics_context.draw_boundary(this.get_shape(), 1.0, inner_border, p_graphics,
                                   Math.max(0.7, layer_visibility));

    // 绘制房间编号
    draw_room_number(p_graphics, p_graphics_context, text_color, text_bg_color, p_intensity);
  }

  /**
   * 绘制房间编号 - 增强版本，带背景和更好的可见性
   */
  private void draw_room_number(Graphics p_graphics, GraphicsContext p_graphics_context,
                               Color p_text_color, Color p_bg_color, double p_intensity)
  {
    try
    {
      // 获取房间的中心点
      app.freerouting.geometry.planar.IntBox bounding_box = this.get_shape().bounding_box();
      if (bounding_box == null) return;

      // 检查房间大小，只在足够大的房间中显示编号
      int room_width = bounding_box.ur.x - bounding_box.ll.x;
      int room_height = bounding_box.ur.y - bounding_box.ll.y;
      if (room_width < 1000 || room_height < 1000) return; // 小于1mm的房间不显示编号

      int center_x = (bounding_box.ll.x + bounding_box.ur.x) / 2;
      int center_y = (bounding_box.ll.y + bounding_box.ur.y) / 2;

      // 转换为屏幕坐标
      java.awt.geom.Point2D screen_point = p_graphics_context.coordinate_transform.board_to_screen(
          new app.freerouting.geometry.planar.FloatPoint(center_x, center_y));

      // 设置字体和颜色
      Graphics2D g2d = (Graphics2D) p_graphics;
      g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING,
                          java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
      g2d.setFont(new java.awt.Font("Arial", java.awt.Font.BOLD, 14));

      // 绘制房间编号文字
      String room_text = "R" + this.id_no;
      java.awt.FontMetrics fm = g2d.getFontMetrics();
      int text_width = fm.stringWidth(room_text);
      int text_height = fm.getHeight();

      int text_x = (int)(screen_point.getX() - text_width/2);
      int text_y = (int)(screen_point.getY() + text_height/4);

      // 绘制文字背景（圆角矩形）
      int padding = 4;
      int bg_x = text_x - padding;
      int bg_y = text_y - text_height + padding;
      int bg_width = text_width + 2 * padding;
      int bg_height = text_height;

      g2d.setColor(p_bg_color);
      g2d.fillRoundRect(bg_x, bg_y, bg_width, bg_height, 6, 6);

      // 绘制文字边框
      g2d.setColor(new Color(0, 80, 0, 255));
      g2d.setStroke(new java.awt.BasicStroke(1.0f));
      g2d.drawRoundRect(bg_x, bg_y, bg_width, bg_height, 6, 6);

      // 绘制文字
      g2d.setColor(p_text_color);
      g2d.drawString(room_text, text_x, text_y);
    }
    catch (Exception e)
    {
      // 忽略绘制错误
    }
  }

  /**
   * Check, if this FreeSpaceExpansionRoom is valid.
   */
  public boolean validate(AutorouteEngine p_autoroute_engine)
  {
    boolean result = true;
    Collection<ShapeTree.TreeEntry> overlapping_objects = new LinkedList<>();
    int[] net_no_arr = new int[1];
    net_no_arr[0] = p_autoroute_engine.get_net_no();
    p_autoroute_engine.autoroute_search_tree.overlapping_tree_entries(this.get_shape(), this.get_layer(), net_no_arr, overlapping_objects);
    for (ShapeTree.TreeEntry curr_entry : overlapping_objects)
    {
      if (curr_entry.object == this)
      {
        continue;
      }
      SearchTreeObject curr_object = (SearchTreeObject) curr_entry.object;
      if (!curr_object.is_trace_obstacle(p_autoroute_engine.get_net_no()))
      {
        continue;
      }
      if (curr_object.shape_layer(curr_entry.shape_index_in_object) != get_layer())
      {
        continue;
      }
      TileShape curr_shape = curr_object.get_tree_shape(p_autoroute_engine.autoroute_search_tree, curr_entry.shape_index_in_object);
      TileShape intersection = this.get_shape().intersection(curr_shape);
      if (intersection.dimension() > 1)
      {
        FRLogger.warn("ExpansionRoom overlap conflict");
        result = false;
      }
    }
    return result;
  }

  /**
   * Removes all doors and target doors from this room.
   */
  @Override
  public void clear_doors()
  {
    super.clear_doors();
    this.target_doors = new LinkedList<>();
  }

  @Override
  public void reset_doors()
  {
    super.reset_doors();
    for (ExpandableObject curr_door : this.target_doors)
    {
      curr_door.reset();
    }
  }
}