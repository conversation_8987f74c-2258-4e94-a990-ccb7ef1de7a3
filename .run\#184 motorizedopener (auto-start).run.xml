<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="#184 motorizedopener (auto-start)" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="app.freerouting.Freerouting" />
    <module name="freerouting.main" />
    <option name="PROGRAM_PARAMETERS" value="-im -de .\tests\Issue184-motorizedopener\freerouting.dsn -do .\tests\Issue184-motorizedopener\freerouting.ses" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="app.freerouting.management.analytics.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>