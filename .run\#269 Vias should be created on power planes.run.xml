<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="#269 Vias should be created on power planes" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="app.freerouting.Freerouting" />
    <module name="freerouting.main" />
    <option name="PROGRAM_PARAMETERS" value="-de .\tests\Issue269-NoViasOnPowerPlanes\Issue269-NoViasOnPowerPlanes.dsn -do .\tests\Issue269-NoViasOnPowerPlanes\Issue269-NoViasOnPowerPlanes.ses" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="app.freerouting.management.analytics.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>