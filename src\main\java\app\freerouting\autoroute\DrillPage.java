package app.freerouting.autoroute;

import app.freerouting.board.Item;
import app.freerouting.board.Pin;
import app.freerouting.board.RoutingBoard;
import app.freerouting.board.ShapeSearchTree;
import app.freerouting.boardgraphics.GraphicsContext;
import app.freerouting.datastructures.ShapeTree.TreeEntry;
import app.freerouting.geometry.planar.IntBox;
import app.freerouting.geometry.planar.Point;
import app.freerouting.geometry.planar.PolylineArea;
import app.freerouting.geometry.planar.TileShape;

import java.awt.*;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;

class DrillPage implements ExpandableObject
{

  /**
   * The shape of the page
   */
  final IntBox shape;
  private final MazeSearchElement[] maze_search_info_arr;
  private final RoutingBoard board;
  /**
   * The list of expansion drills on this page. Null, if not yet calculated.
   */
  private Collection<ExpansionDrill> drills;
  /**
   * The number of the net, for which the drills are calculated
   */
  private int net_no = -1;

  /**
   * Creates a new instance of DrillPage
   */
  public DrillPage(IntBox p_shape, RoutingBoard p_board)
  {
    shape = p_shape;
    board = p_board;
    maze_search_info_arr = new MazeSearchElement[p_board.get_layer_count()];
    for (int i = 0; i < maze_search_info_arr.length; ++i)
    {
      maze_search_info_arr[i] = new MazeSearchElement();
    }
  }

  /**
   * Looks if p_drill_shape contains the center of a drillable Pin on p_layer. Returns null, if no
   * such Pin was found.
   */
  private static Point calc_pin_center_in_drill(TileShape p_drill_shape, int p_layer, RoutingBoard p_board)
  {
    Collection<Item> overlapping_items = p_board.overlapping_items(p_drill_shape, p_layer);
    Point result = null;
    for (Item curr_item : overlapping_items)
    {
      if (curr_item instanceof Pin curr_pin)
      {
        if (curr_pin.drill_allowed() && p_drill_shape.contains_inside(curr_pin.get_center()))
        {
          result = curr_pin.get_center();
        }
      }
    }
    return result;
  }

  /**
   * Returns the drills on this page. If p_attach_smd, drilling to smd pins is allowed.
   */
  public Collection<ExpansionDrill> get_drills(AutorouteEngine p_autoroute_engine, boolean p_attach_smd)
  {
    if (this.drills == null || p_autoroute_engine.get_net_no() != this.net_no)
    {
      this.net_no = p_autoroute_engine.get_net_no();
      this.drills = new LinkedList<>();
      ShapeSearchTree search_tree = this.board.search_tree_manager.get_default_tree();
      Collection<TreeEntry> overlaps = new LinkedList<>();
      search_tree.overlapping_tree_entries(this.shape, -1, overlaps);
      Collection<TileShape> cutout_shapes = new LinkedList<>();
      // drills on top of existing vias are used in the ripup algorithm
      TileShape prev_obstacle_shape = IntBox.EMPTY;
      for (TreeEntry curr_entry : overlaps)
      {
        if (!(curr_entry.object instanceof Item curr_item))
        {
          continue;
        }
        if (curr_item.is_drillable(this.net_no))
        {
          continue;
        }
        if (curr_item instanceof Pin)
        {
          if (p_attach_smd && ((Pin) curr_item).drill_allowed())
          {
            continue;
          }
        }
        TileShape curr_obstacle_shape = curr_item.get_tree_shape(search_tree, curr_entry.shape_index_in_object);
        if (!prev_obstacle_shape.contains(curr_obstacle_shape))
        {
          // Checked to avoid multiple cutout for example for vias with the same shape on all
          // layers.
          TileShape curr_cutout_shape = curr_obstacle_shape.intersection(this.shape);
          if (curr_cutout_shape.dimension() == 2)
          {
            cutout_shapes.add(curr_cutout_shape);
          }
        }
        prev_obstacle_shape = curr_obstacle_shape;
      }
      TileShape[] holes = new TileShape[cutout_shapes.size()];
      Iterator<TileShape> it = cutout_shapes.iterator();
      for (int i = 0; i < holes.length; ++i)
      {
        holes[i] = it.next();
      }
      PolylineArea shape_with_holes = new PolylineArea(this.shape, holes);
      TileShape[] drill_shapes = shape_with_holes.split_to_convex(p_autoroute_engine.stoppable_thread);

      // Use the center points of these drill shapes to try making a via.
      int drill_first_layer = 0;
      int drill_last_layer = this.board.get_layer_count() - 1;
      for (int i = 0; i < drill_shapes.length; ++i)
      {
        TileShape curr_drill_shape = drill_shapes[i];
        Point curr_drill_location = null;
        if (p_attach_smd)
        {
          curr_drill_location = calc_pin_center_in_drill(curr_drill_shape, drill_first_layer, p_autoroute_engine.board);
          if (curr_drill_location == null)
          {
            curr_drill_location = calc_pin_center_in_drill(curr_drill_shape, drill_last_layer, p_autoroute_engine.board);
          }
        }
        if (curr_drill_location == null)
        {
          curr_drill_location = curr_drill_shape.centre_of_gravity().round();
        }
        ExpansionDrill new_drill = new ExpansionDrill(curr_drill_shape, curr_drill_location, drill_first_layer, drill_last_layer);
        if (new_drill.calculate_expansion_rooms(p_autoroute_engine))
        {
          this.drills.add(new_drill);
        }
      }
    }
    return this.drills;
  }

  @Override
  public TileShape get_shape()
  {
    return this.shape;
  }

  @Override
  public int get_dimension()
  {
    return 2;
  }

  @Override
  public int maze_search_element_count()
  {
    return this.maze_search_info_arr.length;
  }

  @Override
  public MazeSearchElement get_maze_search_element(int p_no)
  {
    return this.maze_search_info_arr[p_no];
  }

  /**
   * Resets all drills of this page for autorouting the next connection.
   */
  @Override
  public void reset()
  {
    if (this.drills != null)
    {
      for (ExpansionDrill curr_drill : this.drills)
      {
        curr_drill.reset();
      }
    }
    for (MazeSearchElement curr_info : maze_search_info_arr)
    {
      curr_info.reset();
    }
  }

  /**
   * Invalidates the drills of this page so that they are recalculated at the next call of
   * get_drills().
   */
  public void invalidate()
  {
    this.drills = null;
  }

  /*
   * Test draw of the drills on this page.
   * 绘制此页面上的钻孔点 - 用于调试和可视化
   */
  public void draw(Graphics p_graphics, GraphicsContext p_graphics_context, double p_intensity)
  {
    // 启用钻孔页面绘制 - 用于房间门模型可视化
    if (drills == null)
    {
      return;
    }

    try
    {
      // 绘制页面边界 - 使用更明显的紫色和清晰边界
      Color page_fill_color = new Color(255, 0, 255, 40); // 紫色填充，较透明
      Color page_border_color = new Color(150, 0, 150, 255); // 深紫色边框，完全不透明

      // 填充页面区域
      p_graphics_context.fill_area(this.shape, p_graphics, page_fill_color,
                                  Math.max(0.3, p_intensity * 0.6));

      // 绘制多层边界以增强可见性
      // 外层边界 - 更粗更明显
      p_graphics_context.draw_boundary(this.shape, 5.0, page_border_color, p_graphics,
                                     Math.max(0.8, p_intensity * 0.9));

      // 内层边界 - 细线增强对比
      Color inner_border = new Color(200, 0, 200, 180);
      p_graphics_context.draw_boundary(this.shape, 1.0, inner_border, p_graphics,
                                     Math.max(0.6, p_intensity * 0.7));

      // 绘制过孔（如果已生成）
      for (ExpansionDrill curr_drill : drills)
      {
        curr_drill.draw(p_graphics, p_graphics_context, Math.max(0.8, p_intensity));
      }
    }
    catch (Exception e)
    {
      // 忽略绘制错误，避免影响主程序运行
    }
  }

  @Override
  public CompleteExpansionRoom other_room(CompleteExpansionRoom p_room)
  {
    return null;
  }
}