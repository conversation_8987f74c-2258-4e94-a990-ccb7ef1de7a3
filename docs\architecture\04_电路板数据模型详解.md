# Freerouting 电路板数据模型详解

## 📁 模块位置
`src/main/java/app/freerouting/board/`

## 🎯 模块概述

电路板数据模型是freerouting的数据基础，定义了PCB设计中所有对象的数据结构和操作方法。该模块采用面向对象的设计，将PCB的各种元素抽象为Java类，提供了完整的PCB数据表示和操作能力。

## 🏗️ 核心类层次结构

### PCB对象继承关系

```mermaid
classDiagram
    class Item {
        <<abstract>>
        +int id_no
        +int[] net_no_arr
        +int clearance_class_no
        +FixedState fixed_state
        +draw()
        +get_shape()
        +move_by()
    }
    
    class DrillItem {
        <<abstract>>
        +Point center
        +int first_layer
        +int last_layer
    }
    
    class Connectable {
        <<interface>>
        +get_connection_points()
        +get_trace_connection_cost()
    }
    
    Item <|-- DrillItem
    Item <|-- Trace
    Item <|-- ConductionArea
    Item <|-- ObstacleArea
    
    DrillItem <|-- Pin
    DrillItem <|-- Via
    
    Connectable <|.. Pin
    Connectable <|.. Via
    Connectable <|.. Trace
    
    class Pin {
        +Component component
        +int pin_no
        +boolean changed_to_smd
    }
    
    class Via {
        +boolean attach_allowed
        +ViaInfo via_info
    }
    
    class Trace {
        +Polyline polyline
        +int half_width
        +int layer
    }
```

## 📋 主要类详解

### 1. RoutingBoard.java - 电路板主类

```java
/**
 * 布线电路板类 - PCB数据模型的核心
 * 包含所有PCB对象和操作方法
 */
public class RoutingBoard extends BasicBoard implements Serializable {
    // 自动布线引擎实例
    private transient AutorouteEngine autoroute_engine;
    
    // 变更区域标记（用于优化）
    transient ChangedArea changed_area;
    
    // 推挤失败时的障碍物信息
    private transient Item shove_failing_obstacle;
    private transient int shove_failing_layer = -1;
    
    /**
     * 构造函数 - 创建新的布线电路板
     */
    public RoutingBoard(IntBox p_bounding_box,           // 边界框
                       LayerStructure p_layer_structure,  // 层结构
                       PolylineShape[] p_outline_shapes,   // 外形轮廓
                       int p_outline_cl_class_no,          // 外形间隙类别
                       BoardRules p_rules,                 // 设计规则
                       Communication p_board_communication) { // 通信接口
        super(p_bounding_box, p_layer_structure, p_outline_shapes, 
              p_outline_cl_class_no, p_rules, p_board_communication);
        
        // 初始化变更区域
        this.changed_area = new ChangedArea();
    }
    
    /**
     * 获取自动布线引擎
     */
    public AutorouteEngine get_autoroute_engine() {
        if (autoroute_engine == null) {
            autoroute_engine = new AutorouteEngine(this);
        }
        return autoroute_engine;
    }
    
    /**
     * 插入走线 - 核心布线方法
     */
    public InsertTraceResult insert_trace(Polyline p_polyline,    // 走线路径
                                         int p_half_width,        // 走线半宽
                                         int p_layer,             // 所在层
                                         int[] p_net_no_arr,      // 网络编号数组
                                         int p_clearance_class,   // 间隙类别
                                         FixedState p_fixed_state) { // 固定状态
        
        // 1. 检查输入参数有效性
        if (p_polyline == null || p_polyline.corner_count() < 2) {
            return InsertTraceResult.INVALID_INPUT;
        }
        
        // 2. 检查层有效性
        if (p_layer < 0 || p_layer >= layer_structure.arr.length) {
            return InsertTraceResult.INVALID_LAYER;
        }
        
        // 3. 创建走线对象
        Trace new_trace = new Trace(p_polyline, p_layer, p_half_width,
                                   p_net_no_arr, p_clearance_class, 
                                   get_next_id_no(), p_fixed_state, this);
        
        // 4. 检查设计规则违反
        if (check_trace_violations(new_trace)) {
            return InsertTraceResult.VIOLATION;
        }
        
        // 5. 插入到电路板
        insert_item(new_trace);
        
        // 6. 更新变更区域
        changed_area.join(new_trace.bounding_box());
        
        return InsertTraceResult.SUCCESS;
    }
}
```

**主要功能**：
- **对象管理**: 管理所有PCB对象的生命周期
- **空间索引**: 维护高效的空间查询结构
- **规则检查**: 实时检查设计规则违反
- **变更跟踪**: 跟踪电路板的修改区域

### 2. Item.java - PCB对象基类

```java
/**
 * PCB对象基类 - 所有PCB元素的抽象基类
 * 定义了PCB对象的通用属性和方法
 */
public abstract class Item implements Serializable, SearchTreeObject {
    // 对象唯一标识符
    public final int id_no;
    
    // 所属网络编号数组（支持多网络）
    public int[] net_no_arr;
    
    // 间隙类别编号
    public int clearance_class_no;
    
    // 固定状态（可移动、部分固定、完全固定）
    public FixedState fixed_state;
    
    // 所属电路板引用
    public final BasicBoard board;
    
    /**
     * 构造函数
     */
    protected Item(int[] p_net_no_arr,           // 网络编号数组
                  int p_clearance_class,         // 间隙类别
                  int p_id_no,                   // 对象ID
                  FixedState p_fixed_state,      // 固定状态
                  BasicBoard p_board) {          // 所属电路板
        this.net_no_arr = p_net_no_arr;
        this.clearance_class_no = p_clearance_class;
        this.id_no = p_id_no;
        this.fixed_state = p_fixed_state;
        this.board = p_board;
    }
    
    /**
     * 获取对象的几何形状 - 抽象方法
     */
    public abstract TileShape get_shape();
    
    /**
     * 获取对象的边界框
     */
    public IntBox bounding_box() {
        return get_shape().bounding_box();
    }
    
    /**
     * 移动对象
     */
    public abstract Item move_by(Vector p_vector);
    
    /**
     * 绘制对象
     */
    public abstract void draw(Graphics p_graphics, 
                             GraphicsContext p_graphics_context, 
                             Color[] p_color_arr, 
                             double p_intensity);
    
    /**
     * 检查是否与其他对象相交
     */
    public boolean intersects(Item p_other) {
        // 首先检查边界框
        if (!this.bounding_box().intersects(p_other.bounding_box())) {
            return false;
        }
        
        // 然后检查精确形状
        return this.get_shape().intersects(p_other.get_shape());
    }
    
    /**
     * 计算与其他对象的间隙距离
     */
    public double clearance_distance_to(Item p_other) {
        // 获取间隙规则
        int clearance_class_1 = this.clearance_class_no;
        int clearance_class_2 = p_other.clearance_class_no;
        
        double required_clearance = board.rules.clearance_matrix
            .value(clearance_class_1, clearance_class_2);
        
        // 计算实际距离
        double actual_distance = this.get_shape()
            .distance_to(p_other.get_shape());
        
        return actual_distance - required_clearance;
    }
}
```

### 3. Pin.java - 引脚类

```java
/**
 * 引脚类 - 表示元件的引脚
 * 继承自DrillItem，实现Connectable接口
 */
public class Pin extends DrillItem implements Connectable {
    // 所属元件
    public final Component component;
    
    // 引脚编号
    public final int pin_no;
    
    // 是否已转换为表面贴装
    public boolean changed_to_smd = false;
    
    // 引脚的焊盘堆栈信息
    private final Padstack padstack;
    
    /**
     * 构造函数
     */
    public Pin(Component p_component,        // 所属元件
              int p_pin_no,                 // 引脚编号
              int[] p_net_no_arr,           // 网络编号
              int p_clearance_class,        // 间隙类别
              int p_id_no,                  // 对象ID
              Point p_center,               // 中心点
              Padstack p_padstack,          // 焊盘堆栈
              FixedState p_fixed_state,     // 固定状态
              BasicBoard p_board) {         // 所属电路板
        
        super(p_net_no_arr, p_clearance_class, p_id_no, p_center,
              p_padstack.from_layer(), p_padstack.to_layer(),
              p_fixed_state, p_board);
        
        this.component = p_component;
        this.pin_no = p_pin_no;
        this.padstack = p_padstack;
    }
    
    /**
     * 获取引脚形状
     */
    @Override
    public TileShape get_shape() {
        // 根据层获取相应的焊盘形状
        return padstack.get_shape(layer);
    }
    
    /**
     * 获取连接点 - 实现Connectable接口
     */
    @Override
    public Point[] get_connection_points() {
        // 引脚的连接点就是其中心点
        return new Point[]{center};
    }
    
    /**
     * 计算走线连接成本 - 实现Connectable接口
     */
    @Override
    public double get_trace_connection_cost(int p_trace_half_width, 
                                           int p_layer) {
        // 基础成本
        double base_cost = 1.0;
        
        // 层变换成本
        if (p_layer != this.first_layer && p_layer != this.last_layer) {
            base_cost += 5.0; // 需要过孔的额外成本
        }
        
        // 宽度匹配成本
        double width_factor = (double)p_trace_half_width / padstack.get_min_width();
        if (width_factor > 1.5) {
            base_cost += width_factor; // 宽度不匹配的惩罚
        }
        
        return base_cost;
    }
    
    /**
     * 检查是否可以连接指定宽度的走线
     */
    public boolean can_connect_trace_width(int p_trace_half_width, int p_layer) {
        // 检查焊盘是否足够大
        TileShape pad_shape = padstack.get_shape(p_layer);
        if (pad_shape == null) {
            return false;
        }
        
        IntBox pad_box = pad_shape.bounding_box();
        int min_dimension = Math.min(pad_box.width(), pad_box.height());
        
        return min_dimension >= 2 * p_trace_half_width;
    }
}
```

### 4. Via.java - 过孔类

```java
/**
 * 过孔类 - 表示层间连接的过孔
 * 继承自DrillItem，实现Connectable接口
 */
public class Via extends DrillItem implements Connectable {
    // 是否允许附加走线
    public boolean attach_allowed = true;
    
    // 过孔信息（包含几何和电气特性）
    private final ViaInfo via_info;
    
    /**
     * 构造函数
     */
    public Via(ViaInfo p_via_info,           // 过孔信息
              Point p_center,               // 中心点
              int[] p_net_no_arr,           // 网络编号
              int p_clearance_class,        // 间隙类别
              int p_id_no,                  // 对象ID
              FixedState p_fixed_state,     // 固定状态
              BasicBoard p_board) {         // 所属电路板
        
        super(p_net_no_arr, p_clearance_class, p_id_no, p_center,
              p_via_info.get_padstack().from_layer(),
              p_via_info.get_padstack().to_layer(),
              p_fixed_state, p_board);
        
        this.via_info = p_via_info;
    }
    
    /**
     * 获取过孔形状
     */
    @Override
    public TileShape get_shape() {
        return via_info.get_padstack().get_shape(layer);
    }
    
    /**
     * 获取连接点
     */
    @Override
    public Point[] get_connection_points() {
        return new Point[]{center};
    }
    
    /**
     * 计算走线连接成本
     */
    @Override
    public double get_trace_connection_cost(int p_trace_half_width, 
                                           int p_layer) {
        // 过孔连接的基础成本较高
        double base_cost = 2.0;
        
        // 检查层是否在过孔范围内
        if (p_layer < first_layer || p_layer > last_layer) {
            return Double.MAX_VALUE; // 无法连接
        }
        
        // 宽度匹配成本
        int via_drill_radius = via_info.get_padstack().get_drill_radius();
        if (p_trace_half_width > via_drill_radius) {
            base_cost += 3.0; // 走线比过孔粗的惩罚
        }
        
        return base_cost;
    }
    
    /**
     * 检查过孔是否可以优化（移除或替换）
     */
    public boolean is_optimizable() {
        // 检查是否有多余的层连接
        Set<Integer> connected_layers = get_connected_trace_layers();
        
        // 如果只连接两层，可能可以用更小的过孔替换
        if (connected_layers.size() == 2) {
            return true;
        }
        
        // 如果没有连接任何走线，可以移除
        if (connected_layers.isEmpty()) {
            return true;
        }
        
        return false;
    }
}
```

### 5. Trace.java - 走线类

```java
/**
 * 走线类 - 表示PCB上的导线
 * 实现Connectable接口，支持与其他对象连接
 */
public class Trace extends Item implements Connectable {
    // 走线的多边形路径
    public final Polyline polyline;
    
    // 走线半宽（实际宽度是半宽的两倍）
    public final int half_width;
    
    // 所在层
    public final int layer;
    
    /**
     * 构造函数
     */
    public Trace(Polyline p_polyline,        // 走线路径
                int p_layer,                 // 所在层
                int p_half_width,            // 半宽
                int[] p_net_no_arr,          // 网络编号
                int p_clearance_class,       // 间隙类别
                int p_id_no,                 // 对象ID
                FixedState p_fixed_state,    // 固定状态
                BasicBoard p_board) {        // 所属电路板
        
        super(p_net_no_arr, p_clearance_class, p_id_no, p_fixed_state, p_board);
        
        this.polyline = p_polyline;
        this.layer = p_layer;
        this.half_width = p_half_width;
    }
    
    /**
     * 获取走线形状
     */
    @Override
    public TileShape get_shape() {
        // 将多边形路径扩展为指定宽度的形状
        return polyline.offset_shape(half_width, 0);
    }
    
    /**
     * 获取连接点
     */
    @Override
    public Point[] get_connection_points() {
        // 走线的连接点是其端点
        Point start_point = polyline.first_corner();
        Point end_point = polyline.last_corner();
        return new Point[]{start_point, end_point};
    }
    
    /**
     * 计算走线连接成本
     */
    @Override
    public double get_trace_connection_cost(int p_trace_half_width, 
                                           int p_layer) {
        // 同层连接成本较低
        if (p_layer == this.layer) {
            // 宽度匹配影响成本
            double width_ratio = (double)p_trace_half_width / this.half_width;
            return 1.0 + Math.abs(width_ratio - 1.0);
        }
        
        // 不同层无法直接连接
        return Double.MAX_VALUE;
    }
    
    /**
     * 分割走线 - 在指定点分割为两段
     */
    public Trace[] split_at_point(Point p_split_point) {
        // 找到分割点在多边形上的位置
        int split_index = polyline.get_corner_index_of_point(p_split_point);
        
        if (split_index < 0) {
            return null; // 分割点不在走线上
        }
        
        // 创建两段新的走线
        Polyline first_part = polyline.section(0, split_index + 1);
        Polyline second_part = polyline.section(split_index, polyline.corner_count());
        
        Trace first_trace = new Trace(first_part, layer, half_width,
                                     net_no_arr, clearance_class_no,
                                     board.get_next_id_no(), fixed_state, board);
        
        Trace second_trace = new Trace(second_part, layer, half_width,
                                      net_no_arr, clearance_class_no,
                                      board.get_next_id_no(), fixed_state, board);
        
        return new Trace[]{first_trace, second_trace};
    }
    
    /**
     * 优化走线路径 - 移除不必要的拐点
     */
    public Trace optimize_corners() {
        Polyline optimized_polyline = polyline.remove_unnecessary_corners();
        
        if (optimized_polyline.corner_count() < polyline.corner_count()) {
            return new Trace(optimized_polyline, layer, half_width,
                           net_no_arr, clearance_class_no, id_no, 
                           fixed_state, board);
        }
        
        return this; // 无需优化
    }
}
```

## 🔍 空间索引系统

### ShapeSearchTree.java - 形状搜索树

```java
/**
 * 形状搜索树 - 高效的空间索引结构
 * 用于快速查找指定区域内的PCB对象
 */
public class ShapeSearchTree {
    // 树的根节点
    private TreeNode root;
    
    // 树的深度限制
    private static final int MAX_DEPTH = 20;
    
    /**
     * 插入对象到搜索树
     */
    public void insert(Item p_item) {
        IntBox item_box = p_item.bounding_box();
        insert_recursive(root, p_item, item_box, 0);
    }
    
    /**
     * 查找与指定区域重叠的所有对象
     */
    public Collection<Item> overlapping_objects(IntBox p_search_box) {
        List<Item> result = new ArrayList<>();
        search_recursive(root, p_search_box, result);
        return result;
    }
    
    /**
     * 递归搜索方法
     */
    private void search_recursive(TreeNode p_node, 
                                 IntBox p_search_box, 
                                 List<Item> p_result) {
        if (p_node == null) return;
        
        // 检查节点边界框是否与搜索区域重叠
        if (!p_node.bounding_box.intersects(p_search_box)) {
            return;
        }
        
        // 检查节点中的对象
        for (Item item : p_node.items) {
            if (item.bounding_box().intersects(p_search_box)) {
                p_result.add(item);
            }
        }
        
        // 递归搜索子节点
        for (TreeNode child : p_node.children) {
            search_recursive(child, p_search_box, p_result);
        }
    }
}
```

## 📊 性能特性

### 时间复杂度
- **对象插入**: O(log n)
- **空间查询**: O(log n + k)，k为结果数量
- **碰撞检测**: O(log n)
- **规则检查**: O(log n)

### 空间复杂度
- **对象存储**: O(n)
- **索引结构**: O(n)
- **临时数据**: O(1)

### 优化策略
- **延迟计算**: 形状计算按需进行
- **缓存机制**: 缓存频繁访问的几何数据
- **批量操作**: 支持批量插入和删除
- **增量更新**: 支持增量式索引更新

这个电路板数据模型为freerouting提供了强大而高效的PCB数据表示和操作能力，是整个系统的数据基础。
