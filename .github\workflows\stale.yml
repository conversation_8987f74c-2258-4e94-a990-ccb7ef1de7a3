# This workflow warns and then closes issues and PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: Mark stale issues and pull requests

on:
  schedule:
    - cron: '22 12 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write

    steps:
      - uses: actions/stale@v5
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          days-before-stale: 120
          stale-issue-message: |
            Hey there!👋
            
            This issue is stale because it has been inactive for 120 days.
            If this matter is still relevant, feel free to remove the stale label or add a comment.
            Otherwise, it will be closed in 7 days.

            But remember, with thousands of monthly active users, someone might just have the solution you need.
            This is a community-driven project, and your active participation is crucial.

            If the issue is critical for your work, consider contributing a fix yourself or hiring someone to help.
            I'm here to support your efforts and will review and merge pull requests as quickly as I can.

            Let's collaborate to keep improving our project! 🚀 Your involvement is invaluable, and together, we can ensure the continuous growth and success of our community.

            Thank you for being an integral part of this journey.
            Your engagement is what drives our project forward!
          stale-pr-message: |
            This pull request is stale because it has been inactive for 120 days.
            Remove stale label or comment or this will be closed in 7 days.
          stale-issue-label: 'no-issue-activity'
          stale-pr-label: 'no-pr-activity'
