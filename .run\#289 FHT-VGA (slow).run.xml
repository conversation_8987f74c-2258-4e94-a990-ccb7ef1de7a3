<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="#289 FHT-VGA (slow)" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="app.freerouting.Freerouting" />
    <module name="freerouting.main" />
    <option name="PROGRAM_PARAMETERS" value="-de .\tests\Issue289-Autorouter_PCB_FHT-VGA_2024-03-25.dsn -do .\tests\Issue289-Autorouter_PCB_FHT-VGA_2024-03-25.ses" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="app.freerouting.management.analytics.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>