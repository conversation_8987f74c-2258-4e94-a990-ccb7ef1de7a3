<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="#326 Autostart BBD Mars with job saving enabled" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="app.freerouting.Freerouting" />
    <module name="freerouting.main" />
    <option name="PROGRAM_PARAMETERS" value="-de .\tests\Issue102-Mars-64-revE-rot00.dsn -do .\tests\Issue102-Mars-64-revE-rot00.ses --feature_flags-save_jobs=1 --feature_flags-snapshots=1 --router-save_intermediate_stages=1" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="app.freerouting.management.analytics.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>