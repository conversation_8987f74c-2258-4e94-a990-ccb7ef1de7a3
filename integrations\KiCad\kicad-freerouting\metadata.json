{"$schema": "https://go.kicad.org/pcm/schemas/v1", "name": "Freerouting", "description": "Freerouting KiCad integration", "description_full": "This is the official KiCad integration for Freerouting, an advanced autorouter.\n", "identifier": "app.freerouting.kicad-plugin", "type": "plugin", "author": {"name": "<PERSON><PERSON>", "contact": {"email": "<EMAIL>", "web": "https://www.freerouting.app/"}}, "maintainer": {"name": "<PERSON><PERSON>", "contact": {"email": "<EMAIL>"}}, "license": "GPL-3.0", "resources": {"Github": "https://github.com/freerouting/freerouting/tree/master/integrations/KiCad"}, "versions": [{"version": "2.1.0", "status": "stable", "kicad_version": "6.0", "download_url": "https://github.com/freerouting/freerouting/raw/master/integrations/KiCad/kicad-freerouting-2.1.0.zip"}]}