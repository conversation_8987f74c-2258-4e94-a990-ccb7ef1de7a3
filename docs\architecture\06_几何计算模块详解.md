# Freerouting 几何计算模块详解

## 📁 模块位置
`src/main/java/app/freerouting/geometry/planar/`

## 🎯 模块概述

几何计算模块是freerouting的数学基础，提供了PCB设计中所需的所有几何计算功能。该模块实现了精确的整数几何运算，避免了浮点数精度问题，确保了PCB设计的准确性和可靠性。

## 🏗️ 几何类型层次结构

### 核心几何类继承关系

```mermaid
classDiagram
    class Shape {
        <<abstract>>
        +contains(Point)
        +intersects(Shape)
        +distance_to(Shape)
        +bounding_box()
    }
    
    class TileShape {
        <<abstract>>
        +is_convex()
        +offset_shape(int)
        +split_to_convex()
    }
    
    class ConvexShape {
        <<abstract>>
        +border_line_count()
        +border_line(int)
        +corner_count()
        +corner(int)
    }
    
    Shape <|-- TileShape
    TileShape <|-- ConvexShape
    
    ConvexShape <|-- IntBox
    ConvexShape <|-- IntOctagon
    ConvexShape <|-- Circle
    ConvexShape <|-- Polygon
    
    TileShape <|-- PolygonShape
    TileShape <|-- PolylineShape
    
    class Point {
        <<abstract>>
        +x()
        +y()
        +distance_to(Point)
        +translate_by(Vector)
    }
    
    class Vector {
        <<abstract>>
        +x()
        +y()
        +length()
        +normalize()
        +rotate(Direction)
    }
    
    Point <|-- IntPoint
    Point <|-- RationalPoint
    Point <|-- FloatPoint
    
    Vector <|-- IntVector
    Vector <|-- RationalVector
```

## 📋 主要类详解

### 1. Point.java - 点的抽象基类

```java
/**
 * 点的抽象基类 - 表示二维平面上的点
 * 提供点的基本操作和计算方法
 */
public abstract class Point implements Serializable {
    
    /**
     * 获取X坐标 - 抽象方法
     */
    public abstract double x();
    
    /**
     * 获取Y坐标 - 抽象方法
     */
    public abstract double y();
    
    /**
     * 计算到另一个点的距离
     */
    public double distance_to(Point p_other) {
        double dx = this.x() - p_other.x();
        double dy = this.y() - p_other.y();
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * 计算到另一个点的平方距离（避免开方运算）
     */
    public double distance_square_to(Point p_other) {
        double dx = this.x() - p_other.x();
        double dy = this.y() - p_other.y();
        return dx * dx + dy * dy;
    }
    
    /**
     * 平移点
     */
    public abstract Point translate_by(Vector p_vector);
    
    /**
     * 绕原点旋转
     */
    public abstract Point rotate(Direction p_direction);
    
    /**
     * 检查点是否在指定边界内
     */
    public boolean is_inside_box(IntBox p_box) {
        return p_box.contains(this);
    }
    
    /**
     * 转换为整数点（四舍五入）
     */
    public IntPoint round() {
        return new IntPoint((int) Math.round(x()), (int) Math.round(y()));
    }
    
    /**
     * 转换为有理数点
     */
    public abstract RationalPoint to_rational();
    
    /**
     * 转换为浮点数点
     */
    public FloatPoint to_float() {
        return new FloatPoint(x(), y());
    }
}
```

### 2. IntPoint.java - 整数点类

```java
/**
 * 整数点类 - 使用整数坐标表示的点
 * 提供精确的整数几何运算，避免浮点数精度问题
 */
public class IntPoint extends Point {
    // 坐标值
    public final int x;
    public final int y;
    
    // 零点常量
    public static final IntPoint ZERO = new IntPoint(0, 0);
    
    /**
     * 构造函数
     */
    public IntPoint(int p_x, int p_y) {
        this.x = p_x;
        this.y = p_y;
    }
    
    /**
     * 获取X坐标
     */
    @Override
    public double x() {
        return x;
    }
    
    /**
     * 获取Y坐标
     */
    @Override
    public double y() {
        return y;
    }
    
    /**
     * 平移点
     */
    @Override
    public IntPoint translate_by(Vector p_vector) {
        if (p_vector instanceof IntVector) {
            IntVector int_vector = (IntVector) p_vector;
            return new IntPoint(x + int_vector.x, y + int_vector.y);
        }
        
        // 对于非整数向量，转换为有理数计算
        RationalPoint rational_result = this.to_rational().translate_by(p_vector);
        return rational_result.round();
    }
    
    /**
     * 绕原点旋转
     */
    @Override
    public Point rotate(Direction p_direction) {
        if (p_direction instanceof IntDirection) {
            IntDirection int_dir = (IntDirection) p_direction;
            
            // 使用整数旋转矩阵
            int new_x = x * int_dir.cos() - y * int_dir.sin();
            int new_y = x * int_dir.sin() + y * int_dir.cos();
            
            return new IntPoint(new_x, new_y);
        }
        
        // 对于非整数方向，使用有理数计算
        return this.to_rational().rotate(p_direction);
    }
    
    /**
     * 计算曼哈顿距离
     */
    public int manhattan_distance_to(IntPoint p_other) {
        return Math.abs(x - p_other.x) + Math.abs(y - p_other.y);
    }
    
    /**
     * 计算切比雪夫距离（棋盘距离）
     */
    public int chebyshev_distance_to(IntPoint p_other) {
        return Math.max(Math.abs(x - p_other.x), Math.abs(y - p_other.y));
    }
    
    /**
     * 转换为有理数点
     */
    @Override
    public RationalPoint to_rational() {
        return new RationalPoint(new BigInteger(String.valueOf(x)), 
                                new BigInteger(String.valueOf(y)),
                                BigInteger.ONE);
    }
    
    /**
     * 检查是否为原点
     */
    public boolean is_zero() {
        return x == 0 && y == 0;
    }
    
    /**
     * 获取象限
     */
    public int get_quadrant() {
        if (x >= 0 && y >= 0) return 1;
        if (x < 0 && y >= 0) return 2;
        if (x < 0 && y < 0) return 3;
        return 4;
    }
}
```

### 3. IntBox.java - 整数矩形类

```java
/**
 * 整数矩形类 - 表示轴对齐的矩形区域
 * 广泛用于边界框计算和空间查询
 */
public class IntBox extends ConvexShape {
    // 矩形边界
    public final int ll_x; // 左下角X坐标
    public final int ll_y; // 左下角Y坐标
    public final int ur_x; // 右上角X坐标
    public final int ur_y; // 右上角Y坐标
    
    // 空矩形常量
    public static final IntBox EMPTY = new IntBox(0, 0, -1, -1);
    
    /**
     * 构造函数
     */
    public IntBox(int p_ll_x, int p_ll_y, int p_ur_x, int p_ur_y) {
        this.ll_x = p_ll_x;
        this.ll_y = p_ll_y;
        this.ur_x = p_ur_x;
        this.ur_y = p_ur_y;
    }
    
    /**
     * 从两个点创建矩形
     */
    public static IntBox from_points(IntPoint p1, IntPoint p2) {
        int min_x = Math.min(p1.x, p2.x);
        int min_y = Math.min(p1.y, p2.y);
        int max_x = Math.max(p1.x, p2.x);
        int max_y = Math.max(p1.y, p2.y);
        
        return new IntBox(min_x, min_y, max_x, max_y);
    }
    
    /**
     * 获取矩形宽度
     */
    public int width() {
        return ur_x - ll_x;
    }
    
    /**
     * 获取矩形高度
     */
    public int height() {
        return ur_y - ll_y;
    }
    
    /**
     * 获取矩形面积
     */
    public long area() {
        if (is_empty()) return 0;
        return (long) width() * height();
    }
    
    /**
     * 检查矩形是否为空
     */
    public boolean is_empty() {
        return ur_x < ll_x || ur_y < ll_y;
    }
    
    /**
     * 获取中心点
     */
    public IntPoint centre() {
        return new IntPoint((ll_x + ur_x) / 2, (ll_y + ur_y) / 2);
    }
    
    /**
     * 检查是否包含点
     */
    @Override
    public boolean contains(Point p_point) {
        double x = p_point.x();
        double y = p_point.y();
        
        return x >= ll_x && x <= ur_x && y >= ll_y && y <= ur_y;
    }
    
    /**
     * 检查是否与另一个矩形相交
     */
    public boolean intersects(IntBox p_other) {
        if (this.is_empty() || p_other.is_empty()) {
            return false;
        }
        
        return !(this.ur_x < p_other.ll_x || 
                this.ll_x > p_other.ur_x ||
                this.ur_y < p_other.ll_y || 
                this.ll_y > p_other.ur_y);
    }
    
    /**
     * 计算与另一个矩形的交集
     */
    public IntBox intersection(IntBox p_other) {
        if (!this.intersects(p_other)) {
            return EMPTY;
        }
        
        int new_ll_x = Math.max(this.ll_x, p_other.ll_x);
        int new_ll_y = Math.max(this.ll_y, p_other.ll_y);
        int new_ur_x = Math.min(this.ur_x, p_other.ur_x);
        int new_ur_y = Math.min(this.ur_y, p_other.ur_y);
        
        return new IntBox(new_ll_x, new_ll_y, new_ur_x, new_ur_y);
    }
    
    /**
     * 计算与另一个矩形的并集
     */
    public IntBox union(IntBox p_other) {
        if (this.is_empty()) return p_other;
        if (p_other.is_empty()) return this;
        
        int new_ll_x = Math.min(this.ll_x, p_other.ll_x);
        int new_ll_y = Math.min(this.ll_y, p_other.ll_y);
        int new_ur_x = Math.max(this.ur_x, p_other.ur_x);
        int new_ur_y = Math.max(this.ur_y, p_other.ur_y);
        
        return new IntBox(new_ll_x, new_ll_y, new_ur_x, new_ur_y);
    }
    
    /**
     * 扩展矩形
     */
    public IntBox expand(int p_offset) {
        if (is_empty()) return this;
        
        return new IntBox(ll_x - p_offset, ll_y - p_offset,
                         ur_x + p_offset, ur_y + p_offset);
    }
    
    /**
     * 获取边界框（自身）
     */
    @Override
    public IntBox bounding_box() {
        return this;
    }
    
    /**
     * 获取角点数量
     */
    @Override
    public int corner_count() {
        return 4;
    }
    
    /**
     * 获取指定索引的角点
     */
    @Override
    public Point corner(int p_index) {
        switch (p_index) {
            case 0: return new IntPoint(ll_x, ll_y); // 左下
            case 1: return new IntPoint(ur_x, ll_y); // 右下
            case 2: return new IntPoint(ur_x, ur_y); // 右上
            case 3: return new IntPoint(ll_x, ur_y); // 左上
            default: throw new IllegalArgumentException("角点索引超出范围: " + p_index);
        }
    }
}
```

### 4. Polyline.java - 多段线类

```java
/**
 * 多段线类 - 表示由多个线段连接的路径
 * 广泛用于走线路径的表示和操作
 */
public class Polyline implements Serializable {
    // 角点数组
    private final Point[] corner_arr;
    
    /**
     * 构造函数
     */
    public Polyline(Point[] p_corner_arr) {
        if (p_corner_arr.length < 2) {
            throw new IllegalArgumentException("多段线至少需要2个点");
        }
        this.corner_arr = p_corner_arr.clone();
    }
    
    /**
     * 获取角点数量
     */
    public int corner_count() {
        return corner_arr.length;
    }
    
    /**
     * 获取指定索引的角点
     */
    public Point corner(int p_index) {
        if (p_index < 0 || p_index >= corner_arr.length) {
            throw new IndexOutOfBoundsException("角点索引超出范围: " + p_index);
        }
        return corner_arr[p_index];
    }
    
    /**
     * 获取第一个角点
     */
    public Point first_corner() {
        return corner_arr[0];
    }
    
    /**
     * 获取最后一个角点
     */
    public Point last_corner() {
        return corner_arr[corner_arr.length - 1];
    }
    
    /**
     * 计算多段线的总长度
     */
    public double length() {
        double total_length = 0;
        
        for (int i = 0; i < corner_arr.length - 1; i++) {
            total_length += corner_arr[i].distance_to(corner_arr[i + 1]);
        }
        
        return total_length;
    }
    
    /**
     * 获取边界框
     */
    public IntBox bounding_box() {
        if (corner_arr.length == 0) {
            return IntBox.EMPTY;
        }
        
        double min_x = corner_arr[0].x();
        double min_y = corner_arr[0].y();
        double max_x = min_x;
        double max_y = min_y;
        
        for (int i = 1; i < corner_arr.length; i++) {
            double x = corner_arr[i].x();
            double y = corner_arr[i].y();
            
            min_x = Math.min(min_x, x);
            min_y = Math.min(min_y, y);
            max_x = Math.max(max_x, x);
            max_y = Math.max(max_y, y);
        }
        
        return new IntBox((int) Math.floor(min_x), (int) Math.floor(min_y),
                         (int) Math.ceil(max_x), (int) Math.ceil(max_y));
    }
    
    /**
     * 扩展为指定宽度的形状
     */
    public PolylineShape offset_shape(int p_half_width, int p_clearance) {
        return new PolylineShape(this, p_half_width + p_clearance);
    }
    
    /**
     * 简化多段线 - 移除不必要的角点
     */
    public Polyline simplify() {
        if (corner_arr.length <= 2) {
            return this;
        }
        
        List<Point> simplified_corners = new ArrayList<>();
        simplified_corners.add(corner_arr[0]);
        
        for (int i = 1; i < corner_arr.length - 1; i++) {
            Point prev = corner_arr[i - 1];
            Point curr = corner_arr[i];
            Point next = corner_arr[i + 1];
            
            // 检查三点是否共线
            if (!are_collinear(prev, curr, next)) {
                simplified_corners.add(curr);
            }
        }
        
        simplified_corners.add(corner_arr[corner_arr.length - 1]);
        
        return new Polyline(simplified_corners.toArray(new Point[0]));
    }
    
    /**
     * 检查三点是否共线
     */
    private boolean are_collinear(Point p1, Point p2, Point p3) {
        // 使用叉积判断共线
        double cross_product = (p2.x() - p1.x()) * (p3.y() - p1.y()) - 
                              (p2.y() - p1.y()) * (p3.x() - p1.x());
        
        return Math.abs(cross_product) < 1e-10; // 考虑数值误差
    }
    
    /**
     * 获取指定区间的子多段线
     */
    public Polyline section(int p_start_index, int p_end_index) {
        if (p_start_index < 0 || p_end_index > corner_arr.length || 
            p_start_index >= p_end_index) {
            throw new IllegalArgumentException("无效的区间索引");
        }
        
        Point[] section_corners = new Point[p_end_index - p_start_index];
        System.arraycopy(corner_arr, p_start_index, section_corners, 0, 
                        section_corners.length);
        
        return new Polyline(section_corners);
    }
    
    /**
     * 反转多段线方向
     */
    public Polyline reverse() {
        Point[] reversed_corners = new Point[corner_arr.length];
        
        for (int i = 0; i < corner_arr.length; i++) {
            reversed_corners[i] = corner_arr[corner_arr.length - 1 - i];
        }
        
        return new Polyline(reversed_corners);
    }
}
```

## ⚡ 几何算法

### 1. 点在多边形内判断
```java
public static boolean point_in_polygon(Point p_point, Polygon p_polygon) {
    int intersections = 0;
    Point[] corners = p_polygon.corner_arr;
    
    for (int i = 0; i < corners.length; i++) {
        Point p1 = corners[i];
        Point p2 = corners[(i + 1) % corners.length];
        
        if (ray_intersects_segment(p_point, p1, p2)) {
            intersections++;
        }
    }
    
    return (intersections % 2) == 1;
}
```

### 2. 线段相交检测
```java
public static boolean segments_intersect(Point a1, Point a2, Point b1, Point b2) {
    // 使用方向判断法
    int d1 = direction(b1, b2, a1);
    int d2 = direction(b1, b2, a2);
    int d3 = direction(a1, a2, b1);
    int d4 = direction(a1, a2, b2);
    
    if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
        ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
        return true;
    }
    
    // 检查边界情况
    return (d1 == 0 && on_segment(b1, b2, a1)) ||
           (d2 == 0 && on_segment(b1, b2, a2)) ||
           (d3 == 0 && on_segment(a1, a2, b1)) ||
           (d4 == 0 && on_segment(a1, a2, b2));
}
```

### 3. 凸包算法
```java
public static Polygon convex_hull(Point[] points) {
    if (points.length < 3) {
        throw new IllegalArgumentException("凸包至少需要3个点");
    }
    
    // Graham扫描算法
    Arrays.sort(points, (p1, p2) -> {
        if (p1.y() != p2.y()) {
            return Double.compare(p1.y(), p2.y());
        }
        return Double.compare(p1.x(), p2.x());
    });
    
    // 构建下凸包
    List<Point> lower = new ArrayList<>();
    for (Point p : points) {
        while (lower.size() >= 2 && 
               cross_product(lower.get(lower.size()-2), 
                           lower.get(lower.size()-1), p) <= 0) {
            lower.remove(lower.size() - 1);
        }
        lower.add(p);
    }
    
    // 构建上凸包
    List<Point> upper = new ArrayList<>();
    for (int i = points.length - 1; i >= 0; i--) {
        Point p = points[i];
        while (upper.size() >= 2 && 
               cross_product(upper.get(upper.size()-2), 
                           upper.get(upper.size()-1), p) <= 0) {
            upper.remove(upper.size() - 1);
        }
        upper.add(p);
    }
    
    // 合并结果
    lower.remove(lower.size() - 1);
    upper.remove(upper.size() - 1);
    lower.addAll(upper);
    
    return new Polygon(lower.toArray(new Point[0]));
}
```

## 🔧 精度控制

### 整数几何的优势
- **精确性**: 避免浮点数舍入误差
- **一致性**: 保证计算结果的可重现性
- **性能**: 整数运算比浮点运算更快
- **稳定性**: 避免数值不稳定问题

### 有理数几何
```java
public class RationalPoint extends Point {
    private final BigInteger numerator_x;
    private final BigInteger numerator_y;
    private final BigInteger denominator;
    
    // 提供精确的有理数运算
    public RationalPoint add(RationalPoint other) {
        // 通分后相加
        BigInteger new_denom = this.denominator.multiply(other.denominator);
        BigInteger new_num_x = this.numerator_x.multiply(other.denominator)
                              .add(other.numerator_x.multiply(this.denominator));
        BigInteger new_num_y = this.numerator_y.multiply(other.denominator)
                              .add(other.numerator_y.multiply(this.denominator));
        
        return new RationalPoint(new_num_x, new_num_y, new_denom);
    }
}
```

## 📊 性能优化

### 空间复杂度优化
- **对象池**: 重用常用的几何对象
- **延迟计算**: 按需计算复杂几何属性
- **缓存机制**: 缓存计算结果

### 时间复杂度优化
- **空间索引**: 使用四叉树等结构加速查询
- **早期退出**: 在可能的情况下提前结束计算
- **算法选择**: 根据数据规模选择最优算法

这个几何计算模块为freerouting提供了强大而精确的几何计算能力，是整个系统数学计算的基础。
