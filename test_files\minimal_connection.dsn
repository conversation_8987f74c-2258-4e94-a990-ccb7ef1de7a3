(pcb C:\freerouting_test\minimal_connection.dsn
  (parser
    (string_quote ")
    (space_in_quoted_tokens on)
    (host_cad "KiCad's Pcbnew")
    (host_version "8.0.1")
  )
  (resolution um 10)
  (unit um)
  (structure
    (layer F.Cu
      (type signal)
      (property
        (index 0)
      )
    )
    (boundary
      (path pcb 0  30000 -20000  0 -20000  0 0  30000 0  30000 -20000)
    )
    (via "Via[0-0]_800:400_um")
    (rule
      (width 500)
      (clearance 300)
      (clearance 300 (type default_smd))
      (clearance 50 (type smd_smd))
    )
  )
  (placement
    (component TestPoint:TestPoint_Pad_2.0x2.0mm
      (place PAD1 5000.000000 -10000.000000 front 0.000000 (PN TestPad))
      (place PAD2 25000.000000 -10000.000000 front 0.000000 (PN TestPad))
    )
  )
  (library
    (image TestPoint:TestPoint_Pad_2.0x2.0mm
      (outline (path signal 120  -1200 1200  1200 1200))
      (outline (path signal 120  -1200 -1200  -1200 1200))
      (outline (path signal 120  1200 1200  1200 -1200))
      (outline (path signal 120  1200 -1200  -1200 -1200))
      (pin Round[T]Pad_2000_um 1 0 0)
    )
    (padstack Round[T]Pad_2000_um
      (shape (circle F.Cu 1000))
      (attach off)
    )
    (padstack "Via[0-0]_800:400_um"
      (shape (circle F.Cu 800))
      (attach off)
    )
  )
  (network
    (net TEST_NET
      (pins PAD1-1 PAD2-1)
    )
    (class default
      TEST_NET
      (circuit
        (use_via "Via[0-0]_800:400_um")
      )
      (rule
        (width 500)
        (clearance 300)
      )
    )
  )
  (wiring
  )
)
