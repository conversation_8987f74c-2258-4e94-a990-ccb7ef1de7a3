# Freerouting 项目文档中心

## 📚 文档概述

本文档中心提供了freerouting项目的完整技术文档，包括架构设计、模块详解、API参考和使用指南。所有文档都经过详细编写，包含丰富的代码示例和中文注释，便于开发者理解和使用。

## 🏗️ 架构文档

### 核心架构
- [**01_项目总体架构**](architecture/01_项目总体架构.md) - 项目整体架构设计和模块关系
- [**02_主入口类详解**](architecture/02_主入口类详解.md) - Freerouting.java主入口类的详细分析
- [**03_自动布线模块详解**](architecture/03_自动布线模块详解.md) - 核心布线算法和房间-门模型
- [**04_电路板数据模型详解**](architecture/04_电路板数据模型详解.md) - PCB对象模型和数据结构
- [**05_图形用户界面模块详解**](architecture/05_图形用户界面模块详解.md) - GUI组件和用户交互
- [**06_几何计算模块详解**](architecture/06_几何计算模块详解.md) - 几何算法和精确计算

## 📁 模块结构图

```
freerouting/
├── 🎯 主入口层 (Freerouting.java)
│   ├── 命令行参数解析
│   ├── 应用程序启动控制
│   └── 模式选择 (GUI/Web/CLI)
│
├── 🖥️ 用户界面层 (gui/)
│   ├── BoardFrame - 主窗口框架
│   ├── BoardPanel - 绘图面板
│   ├── BoardMenuBar - 菜单系统
│   ├── BoardToolbar - 工具栏
│   └── WindowXXX - 各种对话框
│
├── 🔄 交互控制层 (interactive/)
│   ├── BoardManager - 电路板管理
│   ├── InteractiveState - 状态机
│   ├── Route - 布线交互
│   └── Selection - 选择操作
│
├── 🧠 核心业务层 (core/)
│   ├── RoutingJob - 布线任务
│   ├── Session - 会话管理
│   ├── BoardLibrary - 元件库
│   └── RouterCounters - 统计计数
│
├── 🤖 自动布线层 (autoroute/)
│   ├── AutorouteEngine - 布线引擎
│   ├── MazeSearchAlgo - 迷宫搜索
│   ├── ExpansionRoom - 房间模型
│   ├── ExpansionDoor - 门模型
│   └── DrillPageArray - 钻孔管理
│
├── 🔲 电路板层 (board/)
│   ├── RoutingBoard - 电路板主类
│   ├── Item - PCB对象基类
│   ├── Pin/Via/Trace - 具体对象
│   ├── ShapeSearchTree - 空间索引
│   └── LayerStructure - 层结构
│
├── 📐 几何计算层 (geometry/)
│   ├── Point/Vector - 基础几何
│   ├── IntBox/Polygon - 形状类
│   ├── Polyline - 多段线
│   └── TileShape - 瓦片形状
│
├── 🎨 图形绘制层 (boardgraphics/)
│   ├── GraphicsContext - 绘图上下文
│   ├── ColorManager - 颜色管理
│   └── CoordinateTransform - 坐标变换
│
├── 📊 数据结构层 (datastructures/)
│   ├── ShapeTree - 空间索引树
│   ├── UndoableObjects - 撤销系统
│   └── TimeLimit - 时间控制
│
├── 📋 规则管理层 (rules/)
│   ├── BoardRules - 设计规则
│   ├── ClearanceMatrix - 间隙矩阵
│   ├── NetClasses - 网络类别
│   └── ViaRules - 过孔规则
│
├── ⚙️ 设置管理层 (settings/)
│   ├── GlobalSettings - 全局设置
│   ├── RouterSettings - 布线设置
│   └── GuiSettings - 界面设置
│
├── 🌐 API接口层 (api/)
│   ├── FreeroutingApplication - Spring Boot应用
│   ├── BaseController - 控制器基类
│   └── v1/ - API版本1
│
├── 📄 文件格式层 (designforms/)
│   ├── specctra/ - Specctra DSN格式
│   └── 其他格式支持
│
└── 🔧 工具层 (logger/, management/, tests/)
    ├── FRLogger - 日志系统
    ├── SessionManager - 会话管理
    ├── TextManager - 多语言支持
    └── BoardValidator - 验证工具
```

## 🔍 关键概念解释

### 房间-门模型 (Room-Door Model)
freerouting的核心算法基于房间-门模型：
- **房间 (Room)**: PCB上的可布线区域，分为自由空间房间和障碍物房间
- **门 (Door)**: 连接两个房间的通道，可以是1维线段或2维区域
- **迷宫搜索**: 在房间-门图中使用A*算法寻找最优路径

### PCB对象层次
- **Item**: 所有PCB对象的基类
- **DrillItem**: 钻孔对象基类 (Pin, Via)
- **Trace**: 走线对象
- **Connectable**: 可连接接口

### 几何精度
- **整数几何**: 使用整数坐标避免浮点误差
- **有理数几何**: 需要精确计算时使用有理数
- **空间索引**: 使用搜索树加速几何查询

## 📖 使用指南

### 开发者快速入门
1. **阅读总体架构** - 了解项目整体设计
2. **理解数据模型** - 掌握PCB对象结构
3. **学习布线算法** - 理解核心算法原理
4. **熟悉GUI框架** - 了解用户界面设计
5. **掌握几何计算** - 理解数学基础

### 代码导航建议
```java
// 从主入口开始
Freerouting.java → main()

// 理解GUI结构
BoardFrame.java → 主窗口
BoardPanel.java → 绘图区域

// 掌握数据模型
RoutingBoard.java → 电路板
Item.java → PCB对象基类

// 学习布线算法
AutorouteEngine.java → 布线引擎
MazeSearchAlgo.java → 搜索算法

// 了解几何计算
Point.java → 点的抽象
IntBox.java → 矩形类
```

## 🔧 开发环境

### 技术栈
- **语言**: Java 17+
- **构建**: Gradle 7.x
- **GUI**: Java Swing
- **Web**: Spring Boot (可选)
- **测试**: JUnit 5

### 开发工具推荐
- **IDE**: IntelliJ IDEA / Eclipse
- **版本控制**: Git
- **调试**: Java调试器
- **性能分析**: JProfiler / VisualVM

## 📝 文档贡献

### 文档规范
- 使用Markdown格式
- 包含详细的代码示例
- 添加中文注释说明
- 提供类图和流程图
- 保持文档与代码同步

### 更新流程
1. 修改相关代码时同步更新文档
2. 添加新功能时编写对应文档
3. 定期检查文档的准确性
4. 收集用户反馈改进文档

## 🎯 学习路径

### 初学者路径
1. 项目总体架构 → 了解整体设计
2. 主入口类详解 → 理解启动流程
3. 图形用户界面 → 熟悉界面操作
4. 电路板数据模型 → 掌握基础概念

### 进阶开发者路径
1. 自动布线模块 → 理解核心算法
2. 几何计算模块 → 掌握数学基础
3. 性能优化技巧 → 提升系统性能
4. 扩展开发指南 → 添加新功能

### 算法研究者路径
1. 房间-门模型原理 → 理解算法基础
2. 迷宫搜索算法 → 掌握路径规划
3. 推挤算法实现 → 学习冲突解决
4. 优化策略分析 → 研究性能提升

## 📞 获取帮助

### 文档问题
- 检查文档是否为最新版本
- 查看相关代码实现
- 参考示例和测试用例

### 技术支持
- 查阅API文档和代码注释
- 参考项目Wiki和FAQ
- 加入开发者社区讨论

---

**注意**: 本文档持续更新中，如发现错误或需要补充，请及时反馈。
