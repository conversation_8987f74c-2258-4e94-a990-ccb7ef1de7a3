<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="#102 BBD Mars with &quot;-oit 0.1&quot;" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="app.freerouting.Freerouting" />
    <module name="freerouting.main" />
    <option name="PROGRAM_PARAMETERS" value="-de .\tests\Issue102-Mars-64-revE-rot00.dsn -do .\tests\Mars-64_oit_0p10000_gte.ses -oit 0.1" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="app.freerouting.management.analytics.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>