package app.freerouting.autoroute;

import app.freerouting.geometry.planar.FloatLine;
import app.freerouting.geometry.planar.FloatPoint;
import app.freerouting.geometry.planar.Point;
import app.freerouting.geometry.planar.TileShape;

/**
 * An ExpansionDoor is a common edge between two ExpansionRooms
 */
public class ExpansionDoor implements ExpandableObject
{

  /**
   * The first room of this door.
   */
  public final ExpansionRoom first_room;
  /**
   * The second room of this door.
   */
  public final ExpansionRoom second_room;
  /**
   * The dimension of a door may be 1 or 2.
   */
  public final int dimension;
  /**
   * each section of the following array can be expanded separately by the maze search algorithm
   */
  MazeSearchElement[] section_arr;

  /**
   * Creates a new instance of ExpansionDoor
   */
  public ExpansionDoor(ExpansionRoom p_first_room, ExpansionRoom p_second_room, int p_dimension)
  {
    first_room = p_first_room;
    second_room = p_second_room;
    dimension = p_dimension;
  }

  /**
   * Creates a new instance of ExpansionDoor
   */
  public ExpansionDoor(ExpansionRoom p_first_room, ExpansionRoom p_second_room)
  {
    first_room = p_first_room;
    second_room = p_second_room;
    dimension = first_room.get_shape().intersection(second_room.get_shape()).dimension();
  }

  /**
   * Calculates the intersection of the shapes of the 2 rooms belonging to this door.
   */
  @Override
  public TileShape get_shape()
  {
    TileShape first_shape = first_room.get_shape();
    TileShape second_shape = second_room.get_shape();
    return first_shape.intersection(second_shape);
  }

  /**
   * The dimension of a door may be 1 or 2. 2-dimensional doors can only exist between
   * ObstacleExpansionRooms
   */
  @Override
  public int get_dimension()
  {
    return this.dimension;
  }

  /**
   * Returns the other room of this door, or null, if p_room is neither equal to this.first_room nor
   * to this.second_room.
   */
  public ExpansionRoom other_room(ExpansionRoom p_room)
  {
    ExpansionRoom result;
    if (p_room == first_room)
    {
      result = second_room;
    }
    else if (p_room == second_room)
    {
      result = first_room;
    }
    else
    {
      result = null;
    }
    return result;
  }

  /**
   * Returns the other room of this door, or null, if p_room is neither equal to this.first_room nor
   * to this.second_room, or if the other room is not a CompleteExpansionRoom.
   */
  @Override
  public CompleteExpansionRoom other_room(CompleteExpansionRoom p_room)
  {
    ExpansionRoom result;
    if (p_room == first_room)
    {
      result = second_room;
    }
    else if (p_room == second_room)
    {
      result = first_room;
    }
    else
    {
      result = null;
    }
    if (!(result instanceof CompleteExpansionRoom))
    {
      result = null;
    }
    return (CompleteExpansionRoom) result;
  }

  @Override
  public int maze_search_element_count()
  {
    return this.section_arr.length;
  }

  @Override
  public MazeSearchElement get_maze_search_element(int p_no)
  {
    return this.section_arr[p_no];
  }

  /**
   * Calculates the Line segments of the sections of this door.
   */
  public FloatLine[] get_section_segments(double p_offset)
  {
    double offset = p_offset + AutorouteEngine.TRACE_WIDTH_TOLERANCE;
    TileShape door_shape = this.get_shape();
    {
      if (door_shape.is_empty())
      {
        return new FloatLine[0];
      }
    }
    FloatLine door_line_segment;
    FloatLine shrinked_line_segment;
    if (this.dimension == 1)
    {
      door_line_segment = door_shape.diagonal_corner_segment();
      shrinked_line_segment = door_line_segment.shrink_segment(offset);
    }
    else if (this.dimension == 2 && this.first_room instanceof CompleteFreeSpaceExpansionRoom && this.second_room instanceof CompleteFreeSpaceExpansionRoom)
    {
      // Overlapping doors at a corner possible in case of 90- or 45-degree routing.
      // In case of freeangle routing the corners are cut off.
      door_line_segment = calc_door_line_segment(door_shape);
      if (door_line_segment == null)
      {
        // CompleteFreeSpaceExpansionRoom inside other room
        return new FloatLine[0];
      }
      if (door_line_segment.b.distance_square(door_line_segment.a) < 4 * offset * offset)
      {
        // door is small, 2 dimensional small doors are not yet expanded.
        return new FloatLine[0];
      }
      shrinked_line_segment = door_line_segment.shrink_segment(offset);
    }
    else
    {
      FloatPoint gravity_point = door_shape.centre_of_gravity();
      door_line_segment = new FloatLine(gravity_point, gravity_point);
      shrinked_line_segment = door_line_segment;
    }
    final double c_max_door_section_width = 10 * offset;
    int section_count = (int) (door_line_segment.b.distance(door_line_segment.a) / c_max_door_section_width) + 1;
    this.allocate_sections(section_count);
    return shrinked_line_segment.divide_segment_into_sections(section_count);
  }

  /**
   * Calculates a diagonal line of the 2-dimensional p_door_shape which represents the restraint
   * line between the shapes of this.first_room and this.second_room.
   */
  private FloatLine calc_door_line_segment(TileShape p_door_shape)
  {
    TileShape first_room_shape = this.first_room.get_shape();
    TileShape second_room_shape = this.second_room.get_shape();
    Point first_corner = null;
    Point second_corner = null;
    int corner_count = p_door_shape.border_line_count();
    for (int i = 0; i < corner_count; ++i)
    {
      Point curr_corner = p_door_shape.corner(i);
      if (!first_room_shape.contains_inside(curr_corner) && !second_room_shape.contains_inside(curr_corner))
      {
        // curr_corner is on the border of both room shapes.
        if (first_corner == null)
        {
          first_corner = curr_corner;
        }
        else if (!first_corner.equals(curr_corner))
        {
          second_corner = curr_corner;
          break;
        }
      }
    }
    if (first_corner == null || second_corner == null)
    {
      return null;
    }
    return new FloatLine(first_corner.to_float(), second_corner.to_float());
  }

  /**
   * Resets this ExpandableObject for autorouting the next connection.
   */
  @Override
  public void reset()
  {
    if (section_arr != null)
    {
      for (MazeSearchElement curr_section : section_arr)
      {
        curr_section.reset();
      }
    }
  }

  /**
   * allocates and initialises p_section_count sections
   */
  void allocate_sections(int p_section_count)
  {
    if (section_arr != null && section_arr.length == p_section_count)
    {
      return; // already allocated
    }
    section_arr = new MazeSearchElement[p_section_count];
    for (int i = 0; i < section_arr.length; ++i)
    {
      section_arr[i] = new MazeSearchElement();
    }
  }

  /**
   * 绘制门的可视化效果
   */
  public void draw(java.awt.Graphics p_graphics, app.freerouting.boardgraphics.GraphicsContext p_graphics_context, double p_intensity)
  {
    try
    {
      app.freerouting.geometry.planar.TileShape door_shape = this.get_shape();
      if (door_shape == null || door_shape.is_empty()) return;

      // 根据门的维度选择不同的颜色
      java.awt.Color door_color;
      java.awt.Color border_color;
      String door_label;

      if (this.dimension == 1)
      {
        // 1维门 - 蓝色线条
        door_color = new java.awt.Color(0, 100, 255, 140); // 蓝色，半透明
        border_color = new java.awt.Color(0, 50, 200, 255); // 深蓝色边框，完全不透明
        door_label = "D1";
      }
      else
      {
        // 2维门 - 黄色区域
        door_color = new java.awt.Color(255, 200, 0, 100); // 黄色，半透明
        border_color = new java.awt.Color(200, 150, 0, 255); // 深黄色边框，完全不透明
        door_label = "D2";
      }

      // 绘制门的形状
      p_graphics_context.fill_area(door_shape, p_graphics, door_color,
                                  Math.max(0.5, p_intensity));

      // 绘制多层边界以增强可见性
      double outer_width = (this.dimension == 1) ? 4.0 : 3.0;
      double inner_width = 1.0;

      // 外层边界 - 更粗更明显
      p_graphics_context.draw_boundary(door_shape, outer_width, border_color, p_graphics,
                                     Math.max(0.9, p_intensity));

      // 内层边界 - 细线增强对比
      java.awt.Color inner_color = (this.dimension == 1) ?
          new java.awt.Color(0, 120, 255, 180) :
          new java.awt.Color(255, 220, 0, 180);
      p_graphics_context.draw_boundary(door_shape, inner_width, inner_color, p_graphics,
                                     Math.max(0.7, p_intensity));

      // 绘制门的标识
      draw_door_label(p_graphics, p_graphics_context, door_label, border_color, p_intensity);
    }
    catch (Exception e)
    {
      // 忽略绘制错误
    }
  }

  /**
   * 绘制门的标识文字
   */
  private void draw_door_label(java.awt.Graphics p_graphics,
                              app.freerouting.boardgraphics.GraphicsContext p_graphics_context,
                              String p_label, java.awt.Color p_color, double p_intensity)
  {
    try
    {
      app.freerouting.geometry.planar.TileShape door_shape = this.get_shape();
      app.freerouting.geometry.planar.IntBox bounding_box = door_shape.bounding_box();
      if (bounding_box == null) return;

      int center_x = (bounding_box.ll.x + bounding_box.ur.x) / 2;
      int center_y = (bounding_box.ll.y + bounding_box.ur.y) / 2;

      // 转换为屏幕坐标
      java.awt.geom.Point2D screen_point = p_graphics_context.coordinate_transform.board_to_screen(
          new app.freerouting.geometry.planar.FloatPoint(center_x, center_y));

      // 设置字体和颜色
      java.awt.Graphics2D g2d = (java.awt.Graphics2D) p_graphics;
      g2d.setColor(p_color);
      g2d.setFont(new java.awt.Font("Arial", java.awt.Font.BOLD, 9));

      // 绘制门标识文字
      java.awt.FontMetrics fm = g2d.getFontMetrics();
      int text_width = fm.stringWidth(p_label);
      int text_height = fm.getHeight();

      g2d.drawString(p_label,
                    (int)(screen_point.getX() - text_width/2),
                    (int)(screen_point.getY() + text_height/4));
    }
    catch (Exception e)
    {
      // 忽略绘制错误
    }
  }
}