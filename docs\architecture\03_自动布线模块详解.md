# Freerouting 自动布线模块详解

## 📁 模块位置
`src/main/java/app/freerouting/autoroute/`

## 🎯 模块概述

自动布线模块是freerouting的核心，实现了先进的PCB自动布线算法。该模块采用基于房间-门模型的迷宫搜索算法，能够高效地为PCB设计生成最优的走线路径。

## 🏗️ 模块架构

### 核心组件关系图

```mermaid
graph TD
    A[AutorouteEngine 布线引擎] --> B[MazeSearchAlgo 迷宫搜索]
    A --> C[ExpansionRoom 扩展房间]
    A --> D[DrillPageArray 钻孔页面]
    
    B --> E[MazeSearchElement 搜索元素]
    B --> F[MazeListElement 搜索列表]
    
    C --> G[FreeSpaceExpansionRoom 自由空间房间]
    C --> H[ObstacleExpansionRoom 障碍物房间]
    
    G --> I[ExpansionDoor 扩展门]
    H --> I
    
    A --> J[BatchAutorouter 批量布线器]
    J --> K[BatchAutorouterThread 布线线程]
```

## 📋 主要类详解

### 1. AutorouteEngine.java - 布线引擎核心

```java
/**
 * 自动布线引擎 - 整个自动布线系统的核心控制器
 * 负责协调各个算法组件，管理布线过程
 */
public class AutorouteEngine {
    // 关联的电路板对象
    public final RoutingBoard board;
    
    // 迷宫搜索算法实例
    private MazeSearchAlgo maze_search_algo;
    
    // 钻孔页面数组，用于过孔布局
    public DrillPageArray drill_page_array;
    
    // 完整扩展房间集合
    private Collection<CompleteFreeSpaceExpansionRoom> complete_expansion_rooms;
    
    // 当前布线的网络编号
    private int current_net_no;
    
    /**
     * 构造函数 - 初始化布线引擎
     */
    public AutorouteEngine(RoutingBoard p_board) {
        this.board = p_board;
        this.maze_search_algo = new MazeSearchAlgo(this);
        this.drill_page_array = new DrillPageArray(p_board);
        this.complete_expansion_rooms = new LinkedList<>();
    }
    
    /**
     * 主要布线方法 - 为指定网络进行自动布线
     */
    public AutorouteAttemptResult autoroute_connection(
            Connection p_connection,
            AutorouteControl p_control,
            int p_net_no) {
        
        // 1. 设置当前网络
        this.current_net_no = p_net_no;
        
        // 2. 清理之前的搜索数据
        this.clear_autoroute_database();
        
        // 3. 执行迷宫搜索
        return this.maze_search_algo.find_connection(
            p_connection, p_control, p_net_no);
    }
}
```

**主要功能**：
- **布线协调**: 协调各个算法组件的工作
- **状态管理**: 管理布线过程中的各种状态
- **资源管理**: 管理内存和计算资源
- **结果输出**: 生成布线结果和统计信息

### 2. MazeSearchAlgo.java - 迷宫搜索算法

```java
/**
 * 迷宫搜索算法 - 基于A*算法的路径搜索
 * 在PCB的房间-门模型中寻找最优连接路径
 */
public class MazeSearchAlgo {
    // 搜索队列，按优先级排序
    private PriorityQueue<MazeSearchElement> search_queue;
    
    // 已访问的房间集合
    private Set<ExpansionRoom> visited_rooms;
    
    // 目标门集合
    private Set<TargetItemExpansionDoor> target_doors;
    
    /**
     * 寻找连接路径的主方法
     */
    public AutorouteAttemptResult find_connection(
            Connection p_connection,
            AutorouteControl p_control,
            int p_net_no) {
        
        // 1. 初始化搜索
        initialize_search(p_connection, p_net_no);
        
        // 2. 执行A*搜索
        while (!search_queue.isEmpty() && !p_control.is_stop_requested()) {
            MazeSearchElement current = search_queue.poll();
            
            // 检查是否到达目标
            if (is_target_reached(current)) {
                return construct_result_path(current);
            }
            
            // 扩展当前节点
            expand_search_element(current, p_control);
        }
        
        // 3. 返回搜索结果
        return new AutorouteAttemptResult(AutorouteAttemptState.NOT_FOUND);
    }
    
    /**
     * 扩展搜索元素 - A*算法的核心
     */
    private void expand_search_element(
            MazeSearchElement p_element,
            AutorouteControl p_control) {
        
        ExpansionRoom current_room = p_element.room;
        
        // 遍历当前房间的所有门
        for (ExpansionDoor door : current_room.get_doors()) {
            ExpansionRoom neighbor_room = door.get_other_room(current_room);
            
            if (neighbor_room != null && !visited_rooms.contains(neighbor_room)) {
                // 计算到邻居房间的成本
                double cost = calculate_cost(p_element, neighbor_room, door);
                
                // 计算启发式估计
                double heuristic = calculate_heuristic(neighbor_room);
                
                // 创建新的搜索元素
                MazeSearchElement new_element = new MazeSearchElement(
                    neighbor_room, p_element, door, cost + heuristic);
                
                search_queue.offer(new_element);
                visited_rooms.add(neighbor_room);
            }
        }
    }
}
```

**算法特点**：
- **A*搜索**: 使用启发式函数优化搜索效率
- **成本计算**: 综合考虑距离、拥挤度、层变换等因素
- **多目标**: 支持同时搜索多个目标点
- **中断机制**: 支持用户中断和超时控制

### 3. ExpansionRoom.java - 扩展房间基类

```java
/**
 * 扩展房间 - 房间-门模型的基础组件
 * 表示PCB上的一个可布线区域
 */
public abstract class ExpansionRoom {
    // 房间形状
    protected TileShape shape;
    
    // 所在层
    protected int layer;
    
    // 连接的门列表
    protected List<ExpansionDoor> doors;
    
    // 房间类型标识
    protected boolean is_obstacle;
    
    /**
     * 获取房间的所有门
     */
    public Collection<ExpansionDoor> get_doors() {
        return doors;
    }
    
    /**
     * 添加门到房间
     */
    public void add_door(ExpansionDoor p_door) {
        if (!doors.contains(p_door)) {
            doors.add(p_door);
        }
    }
    
    /**
     * 检查点是否在房间内
     */
    public boolean contains(Point p_point) {
        return shape.contains(p_point);
    }
    
    /**
     * 计算到目标的距离估计
     */
    public abstract double distance_to_target(Point p_target);
    
    /**
     * 绘制房间（用于可视化）
     */
    public void draw(Graphics p_graphics, 
                    GraphicsContext p_graphics_context, 
                    Color p_color, 
                    double p_intensity) {
        if (shape != null) {
            p_graphics_context.fill_area(shape, p_graphics, p_color, p_intensity);
        }
    }
}
```

### 4. FreeSpaceExpansionRoom.java - 自由空间房间

```java
/**
 * 自由空间扩展房间 - 可以进行布线的区域
 * 继承自ExpansionRoom，表示没有障碍物的空间
 */
public class FreeSpaceExpansionRoom extends ExpansionRoom {
    // 房间中心点
    private Point center_point;
    
    // 房间的最小外接矩形
    private IntBox bounding_box;
    
    /**
     * 构造函数
     */
    public FreeSpaceExpansionRoom(TileShape p_shape, int p_layer) {
        super();
        this.shape = p_shape;
        this.layer = p_layer;
        this.is_obstacle = false;
        this.doors = new ArrayList<>();
        
        // 计算中心点和边界框
        this.bounding_box = p_shape.bounding_box();
        this.center_point = this.bounding_box.centre();
    }
    
    /**
     * 计算到目标的距离估计（启发式函数）
     */
    @Override
    public double distance_to_target(Point p_target) {
        // 使用曼哈顿距离作为启发式估计
        return center_point.distance_to(p_target);
    }
    
    /**
     * 检查房间是否可以容纳指定宽度的走线
     */
    public boolean can_accommodate_trace_width(int p_trace_width) {
        IntBox box = shape.bounding_box();
        return (box.width() >= p_trace_width && box.height() >= p_trace_width);
    }
    
    /**
     * 获取房间的拥挤度（影响布线成本）
     */
    public double get_congestion_factor() {
        // 根据房间大小和已有走线计算拥挤度
        double area = shape.area();
        int trace_count = count_traces_in_room();
        
        return trace_count / area;
    }
}
```

### 5. ExpansionDoor.java - 扩展门

```java
/**
 * 扩展门 - 连接两个房间的通道
 * 在房间-门模型中表示房间之间的连接关系
 */
public class ExpansionDoor {
    // 连接的两个房间
    private ExpansionRoom room_1;
    private ExpansionRoom room_2;
    
    // 门的几何形状（通常是线段或矩形）
    private TileShape door_shape;
    
    // 门的类型（1D线段门或2D区域门）
    private DoorType door_type;
    
    // 门的容量（可通过的最大走线数）
    private int capacity;
    
    // 当前使用的走线数
    private int current_usage;
    
    /**
     * 构造函数 - 创建连接两个房间的门
     */
    public ExpansionDoor(ExpansionRoom p_room_1, 
                        ExpansionRoom p_room_2,
                        TileShape p_shape) {
        this.room_1 = p_room_1;
        this.room_2 = p_room_2;
        this.door_shape = p_shape;
        this.capacity = calculate_capacity();
        this.current_usage = 0;
        
        // 确定门的类型
        this.door_type = (p_shape instanceof LineSegment) ? 
                        DoorType.ONE_DIMENSIONAL : 
                        DoorType.TWO_DIMENSIONAL;
    }
    
    /**
     * 获取门连接的另一个房间
     */
    public ExpansionRoom get_other_room(ExpansionRoom p_room) {
        if (p_room == room_1) {
            return room_2;
        } else if (p_room == room_2) {
            return room_1;
        }
        return null;
    }
    
    /**
     * 检查门是否可用（未达到容量限制）
     */
    public boolean is_available() {
        return current_usage < capacity;
    }
    
    /**
     * 计算通过此门的成本
     */
    public double get_passage_cost(int p_trace_width) {
        // 基础成本
        double base_cost = door_shape.area();
        
        // 拥挤度惩罚
        double congestion_penalty = (double)current_usage / capacity;
        
        // 层变换成本（如果是过孔）
        double layer_change_cost = is_via_door() ? 10.0 : 0.0;
        
        return base_cost * (1.0 + congestion_penalty) + layer_change_cost;
    }
}
```

## 🔄 布线算法流程

### 完整的布线过程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Engine as AutorouteEngine
    participant Maze as MazeSearchAlgo
    participant Room as ExpansionRoom
    participant Door as ExpansionDoor
    
    User->>Engine: 请求布线连接
    Engine->>Engine: 清理之前的数据
    Engine->>Maze: 开始迷宫搜索
    
    Maze->>Maze: 初始化搜索队列
    Maze->>Room: 创建起始房间
    
    loop 搜索循环
        Maze->>Maze: 从队列取出最优元素
        Maze->>Room: 检查是否为目标房间
        alt 找到目标
            Maze->>Engine: 返回成功结果
        else 继续搜索
            Maze->>Door: 获取房间的所有门
            Door->>Room: 获取邻居房间
            Maze->>Maze: 计算成本和启发式值
            Maze->>Maze: 将邻居加入搜索队列
        end
    end
    
    Engine->>User: 返回布线结果
```

### 房间-门模型构建

```mermaid
graph LR
    A[PCB空间] --> B[空间分割]
    B --> C[识别障碍物]
    C --> D[生成自由空间房间]
    D --> E[计算房间邻接关系]
    E --> F[创建连接门]
    F --> G[构建房间-门图]
```

## ⚡ 性能优化策略

### 1. 空间索引优化
```java
// 使用空间索引快速查找相关房间
private ShapeSearchTree room_search_tree;

public Collection<ExpansionRoom> find_rooms_in_area(IntBox p_area) {
    return room_search_tree.overlapping_objects(p_area);
}
```

### 2. 启发式函数优化
```java
// 多层次启发式估计
private double calculate_heuristic(ExpansionRoom p_room, Point p_target) {
    // 几何距离
    double geometric_distance = p_room.distance_to_target(p_target);
    
    // 层变换惩罚
    double layer_penalty = Math.abs(p_room.layer - target_layer) * LAYER_CHANGE_COST;
    
    // 拥挤度影响
    double congestion_factor = p_room.get_congestion_factor();
    
    return geometric_distance + layer_penalty + congestion_factor;
}
```

### 3. 内存管理优化
```java
// 对象池减少内存分配
private ObjectPool<MazeSearchElement> element_pool;

// 及时清理搜索数据
public void clear_autoroute_database() {
    search_queue.clear();
    visited_rooms.clear();
    complete_expansion_rooms.clear();
    
    // 回收对象到池中
    element_pool.returnAll();
}
```

## 🎯 算法特性

### 优势
- **高效搜索**: A*算法保证找到最优路径
- **多层支持**: 原生支持多层PCB布线
- **避障能力**: 智能避开现有走线和障碍物
- **成本优化**: 综合考虑多种成本因素

### 适用场景
- **复杂PCB**: 高密度、多层PCB设计
- **批量布线**: 大量网络的自动布线
- **交互布线**: 实时响应用户操作
- **优化重布**: 现有设计的优化改进

这个自动布线模块体现了现代PCB设计工具的先进算法思想，为用户提供了高质量的自动布线解决方案。
