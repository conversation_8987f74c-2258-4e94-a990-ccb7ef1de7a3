﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7f05d012-5b3d-433b-bcb5-64d3f21a053c}</ProjectGuid>
    <ProjectHome />
    <StartupFile>installjava.py</StartupFile>
    <SearchPath />
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <ProjectTypeGuids>{888888a0-9f3d-457c-b088-3a5042f75d52}</ProjectTypeGuids>
    <LaunchProvider>Standard Python launcher</LaunchProvider>
    <InterpreterId />
    <Name>installjava</Name>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'" />
  <PropertyGroup Condition="'$(Configuration)' == 'Release'" />
  <PropertyGroup>
    <VisualStudioVersion Condition=" '$(VisualStudioVersion)' == '' ">10.0</VisualStudioVersion>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="checkjava.py" />
    <Compile Include="installjava.py" />
    <Compile Include="kicad-freerouting\plugins\plugin.py" />
    <Compile Include="kicad-freerouting\plugins\__init__.py" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="kicad-freerouting\plugins\icon_24x24.png" />
    <Content Include="kicad-freerouting\plugins\plugin.ini" />
    <Content Include="kicad-freerouting\resources\icon.png" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="kicad-freerouting\" />
    <Folder Include="kicad-freerouting\plugins" />
    <Folder Include="kicad-freerouting\resources" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
</Project>