@echo off
chcp 65001 >nul
echo ========================================
echo Freerouting 房间门调试版本编译脚本
echo ========================================
echo.

:: 检查Gradle环境
echo [1/3] 检查编译环境...
if not exist "gradlew.bat" (
    echo ❌ 错误: 未找到gradlew.bat
    echo 请确保在freerouting项目根目录下运行此脚本
    pause
    exit /b 1
)
echo ✅ Gradle环境检查通过

:: 清理之前的构建
echo.
echo [2/3] 清理之前的构建...
call .\gradlew.bat clean
if %errorlevel% neq 0 (
    echo ❌ 清理失败
    pause
    exit /b 1
)
echo ✅ 清理完成

:: 编译项目
echo.
echo [3/3] 编译房间门调试版本...
echo 正在编译，请稍候...
call .\gradlew.bat build -x test
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    echo 请检查错误信息并修复问题
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ 编译成功！
echo ========================================
echo.
echo 📁 生成的文件:
echo    build\libs\freerouting-executable.jar
echo.
echo 🚀 下一步操作:
echo 1. 运行启动脚本: scripts\启动房间门调试模式.bat
echo 2. 或直接运行: java -jar build\libs\freerouting-executable.jar
echo.
echo 📋 房间门调试功能已启用:
echo ✅ DrillPage绘制功能已启用
echo ✅ AutorouteEngine绘制功能已启用  
echo ✅ 菜单项"房间门模型调试"已添加
echo ✅ GuiBoardManager调试方法已添加
echo.
echo 📖 使用指南: docs\usage\房间门模型可视化调试指南.md
echo.
pause
