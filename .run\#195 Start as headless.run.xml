<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="#195 Start as headless" type="Application" factoryName="Application">
    <option name="ALTERNATIVE_JRE_PATH" value="temurin-17" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="MAIN_CLASS_NAME" value="app.freerouting.Freerouting" />
    <module name="freerouting.main" />
    <option name="VM_PARAMETERS" value="-Djava.awt.headless=true" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="app.freerouting.management.analytics.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>