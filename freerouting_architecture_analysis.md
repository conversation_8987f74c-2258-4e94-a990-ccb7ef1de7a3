# Freerouting 代码架构与算法流程详细分析

## 1. 项目概述

Freerouting 是一个强大的PCB自动布线器，兼容任何支持标准Specctra或Electra DSN接口的PCB设计软件。它导入由主机系统的Specctra接口生成的`.dsn`文件，并导出`.ses` Specctra会话文件。

### 1.1 核心特性
- 支持多层PCB布线
- 兼容标准DSN/SES格式
- 提供GUI、CLI和API三种交互方式
- 支持45度、90度和自由角度布线
- 集成推挤算法和优化算法

## 2. 整体架构

### 2.1 模块组织结构

```
app.freerouting/
├── api/                    # REST API接口
├── autoroute/             # 核心布线算法
├── board/                 # PCB板级数据结构
├── core/                  # 核心业务逻辑
├── designforms/           # 文件格式处理
├── geometry/              # 几何计算库
├── gui/                   # 图形用户界面
├── interactive/           # 交互式操作
├── logger/                # 日志系统
├── management/            # 系统管理
├── rules/                 # 设计规则
└── settings/              # 配置管理
```

### 2.2 核心类层次结构

#### 2.2.1 主入口类
```java
app.freerouting.Freerouting
├── 初始化全局设置
├── 启动GUI/CLI/API服务
└── 管理应用生命周期
```

#### 2.2.2 核心数据结构
```java
BasicBoard (基础板类)
├── RoutingBoard (布线板类)
├── BoardLibrary (板库)
├── LayerStructure (层结构)
└── BoardRules (设计规则)
```

#### 2.2.3 布线引擎
```java
AutorouteEngine (布线引擎)
├── MazeSearchAlgo (迷宫搜索算法)
├── LocateFoundConnectionAlgo (路径定位算法)
└── InsertFoundConnectionAlgo (走线插入算法)
```

## 3. 核心算法流程

### 3.1 总体布线流程

```mermaid
graph TD
    A[DSN文件输入] --> B[文件解析]
    B --> C[数据结构初始化]
    C --> D[扇出阶段]
    D --> E[主要布线阶段]
    E --> F[后处理优化]
    F --> G[SES文件输出]
    
    D --> D1[SMD引脚扇出]
    E --> E1[批量布线]
    E --> E2[迷宫搜索]
    E --> E3[路径回溯]
    E --> E4[走线插入]
    F --> F1[Pull-tight优化]
    F --> F2[过孔优化]
```

### 3.2 迷宫搜索算法详解

#### 3.2.1 算法初始化
```java
// MazeSearchAlgo.java
public static MazeSearchAlgo get_instance(Set<Item> p_start_items, 
                                         Set<Item> p_destination_items, 
                                         AutorouteEngine p_autoroute_database, 
                                         AutorouteControl p_ctrl)
{
    MazeSearchAlgo new_instance = new MazeSearchAlgo(p_autoroute_database, p_ctrl);
    if (new_instance.init(p_start_items, p_destination_items)) {
        return new_instance;
    }
    return null;
}
```

#### 3.2.2 搜索主循环
```java
public Result find_connection()
{
    while (occupy_next_element()) {
        // 持续扩展直到找到目标或扩展列表为空
    }
    if (this.destination_door == null) {
        return null;
    }
    return new Result(this.destination_door, this.section_no_of_destination_door);
}
```

#### 3.2.3 房间-门模型
- **房间(Room)**: 表示可布线的连续区域
  - `FreeSpaceExpansionRoom`: 自由空间房间
  - `ObstacleExpansionRoom`: 障碍物房间
- **门(Door)**: 连接房间的通道
  - `ExpansionDoor`: 普通扩展门
  - `TargetItemExpansionDoor`: 目标门

### 3.3 路径回溯与几何计算

#### 3.3.1 回溯数组构建
```java
// LocateFoundConnectionAlgo.java
private static Collection<BacktrackElement> backtrack(MazeSearchAlgo.Result p_maze_search_result)
{
    Collection<BacktrackElement> result = new LinkedList<>();
    ExpandableObject curr_backtrack_door = p_maze_search_result.door;
    
    // 沿着回溯链追溯到起点
    for (;;) {
        result.add(curr_backtrack_element);
        curr_backtrack_door = curr_maze_search_element.backtrack_door;
        if (curr_backtrack_door == null) {
            break; // 到达起点
        }
        // 继续回溯...
    }
    return result;
}
```

#### 3.3.2 45度角点计算
```java
// LocateFoundConnectionAlgo45Degree.java
@Override
protected Collection<FloatPoint> calculate_next_trace_corners()
{
    Collection<FloatPoint> result = new LinkedList<>();
    
    // 1. 收缩房间形状以考虑走线宽度
    TileShape shrinked_room_shape = room_shape.offset(-shrink_offset);
    
    // 2. 计算最近点
    FloatPoint nearest_room_point = shrinked_room_shape.nearest_point_approx(current_from_point);
    
    // 3. 添加45度角点
    result.add(calculate_additional_corner(current_from_point, nearest_room_point, 
                                         horizontal_first, angle_restriction));
    return result;
}
```

### 3.4 走线插入算法

#### 3.4.1 强制插入策略
```java
// InsertFoundConnectionAlgo.java
public static Result insert_connection(LocateFoundConnectionAlgo.Result p_connection, 
                                     RoutingBoard p_board, 
                                     AutorouteControl p_ctrl)
{
    for (ResultItem curr_new_item : p_connection.connection_items) {
        // 1. 插入层间过孔
        if (!new_instance.insert_via(curr_new_item.corners[0], curr_layer, curr_new_item.layer)) {
            return null;
        }
        
        // 2. 插入走线段
        if (!new_instance.insert_trace(curr_new_item)) {
            return null;
        }
    }
    return new_instance;
}
```

#### 3.4.2 推挤算法
```java
private boolean insert_trace(ResultItem p_trace)
{
    Point ok_point = board.insert_forced_trace_polyline(
        insert_polyline, 
        ctrl.trace_half_width[p_trace.layer], 
        p_trace.layer, 
        net_no_arr, 
        ctrl.trace_clearance_class_no, 
        ctrl.max_shove_trace_recursion_depth,  // 推挤递归深度
        ctrl.max_shove_via_recursion_depth, 
        ctrl.max_spring_over_recursion_depth,  // 跨越递归深度
        Integer.MAX_VALUE, 
        ctrl.pull_tight_accuracy, 
        true, 
        null
    );
    
    return ok_point != insert_polyline.first_corner();
}
```

## 4. 设计模式与架构特点

### 4.1 主要设计模式

#### 4.1.1 策略模式
- `AngleRestriction`: 角度约束策略
  - `NINETY_DEGREE`: 90度约束
  - `FORTYFIVE_DEGREE`: 45度约束
  - `NONE`: 自由角度

#### 4.1.2 工厂模式
- `LocateFoundConnectionAlgo.get_instance()`: 根据角度约束创建相应算法
- `MazeSearchAlgo.get_instance()`: 创建迷宫搜索实例

#### 4.1.3 观察者模式
- `BoardObservers`: 板级事件观察者
- `TaskStateChangedEvent`: 任务状态变化事件

### 4.2 数据结构特点

#### 4.2.1 几何精确性
- 使用精确整数坐标系统
- 支持复杂多边形运算
- 实现高效的空间索引(`ShapeSearchTree`)

#### 4.2.2 内存管理
- 使用对象池减少GC压力
- 实现增量式数据结构更新
- 支持撤销/重做操作

## 5. 性能优化策略

### 5.1 算法优化
- **A*启发式搜索**: 使用距离估算加速搜索
- **分层处理**: 按层分别处理减少复杂度
- **增量更新**: 只重新计算变化区域

### 5.2 数据结构优化
- **空间索引**: 使用八叉树加速空间查询
- **缓存机制**: 缓存房间和门的计算结果
- **批量操作**: 批量处理减少函数调用开销

## 6. 扩展性设计

### 6.1 插件架构
- 支持自定义布线策略
- 可扩展的文件格式支持
- 模块化的优化算法

### 6.2 API设计
- RESTful API接口
- 事件驱动的异步处理
- 标准化的数据交换格式

## 7. 关键技术细节

### 7.1 房间完成算法
```java
// ShapeSearchTree45Degree.java
@Override
public Collection<IncompleteFreeSpaceExpansionRoom> complete_shape(
    IncompleteFreeSpaceExpansionRoom p_room,
    int p_net_no,
    SearchTreeObject p_ignore_object,
    TileShape p_ignore_shape)
{
    // 1. 确定必须包含的形状
    TileShape contained_shape = p_room.get_contained_shape();
    IntOctagon shape_to_be_contained = contained_shape.bounding_octagon();

    // 2. 计算最大可用空间
    IntOctagon start_shape = board.get_bounding_box().bounding_octagon();
    IntOctagon[] max_shapes = this.calc_max_shapes(shape_to_be_contained,
                                                   start_shape, p_net_no,
                                                   p_ignore_object, p_ignore_shape);

    // 3. 为每个最大形状创建房间
    Collection<IncompleteFreeSpaceExpansionRoom> result = new LinkedList<>();
    for (IntOctagon curr_max_shape : max_shapes) {
        if (curr_max_shape.dimension() == 2) {
            result.add(new IncompleteFreeSpaceExpansionRoom(curr_max_shape,
                                                           p_room.get_layer(),
                                                           shape_to_be_contained));
        }
    }
    return result;
}
```

### 7.2 过孔插入算法
```java
// ForcedViaAlgo.java
public static boolean insert(ViaInfo p_via_info, Point p_location,
                           int[] p_net_no_arr, int p_trace_clearance_class_no,
                           int[] p_trace_pen_halfwidth_arr,
                           int p_max_recursion_depth,
                           int p_max_via_recursion_depth,
                           RoutingBoard p_board)
{
    Vector translate_vector = p_location.difference_by(Point.ZERO);

    // 1. 检查每一层的可行性
    for (int layer_no = p_via_info.get_padstack().from_layer();
         layer_no <= p_via_info.get_padstack().to_layer(); ++layer_no) {

        // 2. 获取该层的焊盘形状
        ConvexShape curr_pad_shape = p_via_info.get_padstack().get_shape(layer_no);
        if (curr_pad_shape == null) continue;

        // 3. 平移到目标位置
        curr_pad_shape = (ConvexShape) curr_pad_shape.translate_by(translate_vector);

        // 4. 强制插入焊盘
        if (!ForcedPadAlgo.insert_pad(curr_pad_shape, layer_no, p_net_no_arr,
                                     p_trace_clearance_class_no,
                                     p_trace_pen_halfwidth_arr,
                                     p_max_recursion_depth,
                                     p_max_via_recursion_depth, p_board)) {
            return false;
        }
    }

    // 5. 创建过孔对象
    Via new_via = p_board.insert_via(p_via_info.get_padstack(), p_location,
                                    p_net_no_arr, p_trace_clearance_class_no,
                                    FixedState.UNFIXED, true);
    return new_via != null;
}
```

### 7.3 Pull-tight优化算法
```java
// PullTightAlgo45.java
@Override
Polyline pull_tight(Polyline p_polyline)
{
    Polyline new_result = avoid_acid_traps(p_polyline);  // 避免酸蚀陷阱
    Polyline prev_result = null;

    // 迭代优化直到收敛
    while (new_result != prev_result && !this.is_stop_requested()) {
        prev_result = new_result;
        Polyline tmp1 = reduce_corners(prev_result);      // 减少角点
        Polyline tmp2 = smoothen_corners(tmp1);           // 平滑角点
        new_result = reposition_lines(tmp2);              // 重新定位线段
    }
    return new_result;
}

private Polyline reduce_corners(Polyline p_polyline)
{
    if (p_polyline.arr.length <= 4) {
        return p_polyline;
    }

    // 尝试移除不必要的角点
    Point[] new_corners = new Point[p_polyline.arr.length - 1];
    boolean corner_removed = false;

    for (int i = 1; i < p_polyline.arr.length - 2; ++i) {
        Point prev_corner = p_polyline.corner(i - 1);
        Point curr_corner = p_polyline.corner(i);
        Point next_corner = p_polyline.corner(i + 1);

        // 检查是否可以直接连接prev_corner和next_corner
        if (can_skip_corner(prev_corner, curr_corner, next_corner)) {
            // 跳过当前角点
            corner_removed = true;
            continue;
        }
        new_corners[new_corner_count++] = curr_corner;
    }

    if (corner_removed) {
        return new Polyline(Arrays.copyOf(new_corners, new_corner_count));
    }
    return p_polyline;
}
```

## 8. 文件格式处理

### 8.1 DSN文件解析
```java
// DsnFile.java
public static ReadResult read(InputStream inputStream,
                            BoardManager boardManager,
                            BoardObservers boardObservers,
                            IdentificationNumberGenerator identificationNumberGenerator)
{
    IJFlexScanner dsnFlexScanner = new SpecctraDsnStreamReader(inputStream);

    // 1. 验证文件格式
    for (int i = 0; i < 3; ++i) {
        curr_token = dsnFlexScanner.next_token();
        if (i == 0) keyword_ok = (curr_token == Keyword.OPEN_BRACKET);
        else if (i == 1) keyword_ok = (curr_token == Keyword.PCB_SCOPE);
    }

    // 2. 创建读取参数
    ReadScopeParameter read_scope_par = new ReadScopeParameter(
        dsnFlexScanner, boardManager, boardObservers, identificationNumberGenerator);

    // 3. 解析文件内容
    boolean read_ok = Keyword.PCB_SCOPE.read_scope(read_scope_par);

    return read_ok ? ReadResult.OK : ReadResult.ERROR;
}
```

### 8.2 SES文件输出
```java
// SpecctraSesFileWriter.java
public static void write(BoardManager p_board_handling,
                        OutputStream p_output_stream,
                        String p_design_name) throws IOException
{
    IndentFileWriter output_file = new IndentFileWriter(p_output_stream);

    // 1. 写入文件头
    output_file.start_scope();
    output_file.write("session ");
    output_file.write(p_design_name);

    // 2. 写入布线结果
    write_routes(p_board_handling.get_routing_board(), output_file);

    // 3. 写入过孔信息
    write_vias(p_board_handling.get_routing_board(), output_file);

    // 4. 写入文件尾
    output_file.end_scope();
    output_file.close();
}
```

## 9. 并发与性能

### 9.1 多线程处理
```java
// AutorouterAndRouteOptimizerThread.java
public class AutorouterAndRouteOptimizerThread extends Thread
{
    private volatile boolean stop_auto_router_requested = false;

    @Override
    public void run()
    {
        try {
            // 1. 扇出阶段
            if (routerSettings.getRunFanout()) {
                batchFanout.runBatchLoop();
            }

            // 2. 主要布线阶段
            if (routerSettings.getRunRouter() && !is_stop_auto_router_requested()) {
                batchAutorouter.runBatchLoop();
            }

            // 3. 后处理优化
            if (routerSettings.getRunOptimizer() && !is_stop_auto_router_requested()) {
                batchOptimizer.runBatchLoop();
            }

        } catch (Exception e) {
            FRLogger.error("AutorouterAndRouteOptimizerThread.run: Exception", e);
        }
    }

    public boolean is_stop_auto_router_requested()
    {
        return stop_auto_router_requested;
    }

    public void request_stop()
    {
        stop_auto_router_requested = true;
    }
}
```

### 9.2 内存优化
```java
// AutorouteEngine.java
public void clear()
{
    if (this.complete_expansion_rooms != null) {
        for (CompleteFreeSpaceExpansionRoom curr_room : this.complete_expansion_rooms) {
            curr_room.remove_from_tree(this.autoroute_search_tree);
        }
        this.complete_expansion_rooms.clear();
    }

    if (this.incomplete_expansion_rooms != null) {
        this.incomplete_expansion_rooms.clear();
    }

    this.drill_page_array.clear();

    // 强制垃圾回收
    System.gc();
}
```

## 10. 总结

Freerouting采用了先进的几何算法和数据结构设计，实现了高效、精确的PCB自动布线。其核心优势包括：

1. **几何精确性**: 基于精确几何计算，避免网格近似误差
2. **算法先进性**: 使用房间-门模型的迷宫搜索算法
3. **架构灵活性**: 模块化设计支持多种使用场景
4. **性能优化**: 多层次的性能优化策略
5. **标准兼容**: 完全兼容工业标准DSN/SES格式

这种设计使得Freerouting能够处理现代复杂PCB设计的各种挑战，为PCB设计自动化提供了强有力的技术支撑。通过深入理解其架构和算法，开发者可以更好地使用、扩展和优化这个强大的布线工具。
