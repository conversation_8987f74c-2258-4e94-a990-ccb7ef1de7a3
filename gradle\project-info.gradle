// -----------------------------------------------------------------------------
// publishing information
// -----------------------------------------------------------------------------
ext.publishing.artifactId = project.name.toLowerCase()
ext.publishing.groupId = 'app.freerouting'
ext.publishing.versionId = '2.1.2-SNAPSHOT'
ext.publishing.isReleaseVersion = !ext.publishing.versionId.endsWith("SNAPSHOT")

ext.publishing.developerName = '<PERSON><PERSON>, <PERSON>, <PERSON>, Alfons Wirtz'
ext.publishing.developerAlias = 'freerouting'
ext.publishing.developerEmail = '<EMAIL>'
ext.publishing.inceptionYear = '2004'

ext.publishing.desc = 'Advanced PCB Autorouter'
ext.publishing.license = 'GPL-3.0'
ext.publishing.licenseUrl = 'https://github.com/freerouting/freerouting/blob/master/LICENSE.txt'
ext.publishing.labels = ['PCB', 'autorouter', 'KiCad', 'EAGLE', 'router', 'routing', 'routing-engine', 'dsn', 'autoroute', 'auto-router', 'pcb-design', 'specctra']
ext.publishing.websiteUrl = 'https://github.com/freerouting/freerouting'
ext.publishing.issueTrackerUrl = 'https://github.com/freerouting/freerouting/issues'
ext.publishing.vcsUrl = 'https://github.com/freerouting/freerouting.git'

ext.publishing.pomName = publishing.artifactId