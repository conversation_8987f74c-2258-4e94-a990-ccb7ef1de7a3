(pcb C:\freerouting_test\simple_two_pins.dsn
  (parser
    (string_quote ")
    (space_in_quoted_tokens on)
    (host_cad "KiCad's Pcbnew")
    (host_version "8.0.1")
  )
  (resolution um 10)
  (unit um)
  (structure
    (layer F.Cu
      (type signal)
      (property
        (index 0)
      )
    )
    (boundary
      (path pcb 0  40000 -25000  5000 -25000  5000 -5000  40000 -5000  40000 -25000)
    )
    (via "Via[0-0]_800:400_um")
    (rule
      (width 250)
      (clearance 200)
      (clearance 200 (type default_smd))
      (clearance 50 (type smd_smd))
    )
  )
  (placement
    (component Connector_PinHeader_2.54mm:PinHeader_1x02_P2.54mm_Vertical
      (place J1 10000.000000 -15000.000000 front 0.000000 (PN Conn_01x02))
      (place J2 30000.000000 -15000.000000 front 0.000000 (PN Conn_01x02))
    )
  )
  (library
    (image Connector_PinHeader_2.54mm:PinHeader_1x02_P2.54mm_Vertical
      (outline (path signal 120  -1330 -1270  -1330 -3870))
      (outline (path signal 120  -1330 -1270  1330 -1270))
      (outline (path signal 120  -1330 -3870  1330 -3870))
      (outline (path signal 120  1330 -1270  1330 -3870))
      (outline (path signal 50  -1800 1800  -1800 -4350))
      (outline (path signal 50  -1800 1800  1800 1800))
      (outline (path signal 50  1800 -4350  -1800 -4350))
      (outline (path signal 50  1800 -4350  1800 1800))
      (pin Rect[A]Pad_1700x1700_um 1 0 0)
      (pin Oval[A]Pad_1700x1700_um 2 0 -2540)
    )
    (padstack Rect[A]Pad_1700x1700_um
      (shape (rect F.Cu -850 -850 850 850))
      (attach off)
    )
    (padstack Oval[A]Pad_1700x1700_um
      (shape (circle F.Cu 850))
      (attach off)
    )
    (padstack "Via[0-0]_800:400_um"
      (shape (circle F.Cu 800))
      (attach off)
    )
  )
  (network
    (net "Net-1"
      (pins J1-1 J2-1)
    )
    (net "Net-2"
      (pins J1-2 J2-2)
    )
    (class default
      "Net-1" "Net-2"
      (circuit
        (use_via "Via[0-0]_800:400_um")
      )
      (rule
        (width 250)
        (clearance 200)
      )
    )
  )
  (wiring
  )
)
