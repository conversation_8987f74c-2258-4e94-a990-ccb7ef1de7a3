# KiCad Freerouting Plugin

## Installation

1. Open KiCad 6.0 or newer

2. Start Tools / Plugin and Content Manager (Ctrl+M)

![image](https://user-images.githubusercontent.com/910321/*********-9856712b-f5c8-497e-9bfa-3f869dae85bc.png)

3. Search for the Freerouting plugin

![image](https://user-images.githubusercontent.com/910321/*********-8bfdaeed-ea17-4e3f-b998-b5e52c04b2c0.png)

4. Click on the Install button

![image](https://user-images.githubusercontent.com/910321/*********-0e006f1c-dfb9-4fd1-994c-8e6e0b4cb56a.png)

5. Done!

## Operation

1. Open you PCB design in PCB Editor

2. (Optional) Remove routed tracks and via from the design

![image](https://user-images.githubusercontent.com/910321/*********-ccf3c688-d364-470b-bfca-03dd049919b1.png)

3. Start Freerouting from the Tools / External Plugins menu

![image](https://user-images.githubusercontent.com/910321/*********-cbf652bf-428a-4648-b455-5ebba78be920.png)

4. Wait until the Freerouting app exits and the plugin loads your routed design

![image](https://user-images.githubusercontent.com/910321/*********-d32fb974-e3e6-4e65-832e-ed033ef3b3db.png)

5. Enjoy!
