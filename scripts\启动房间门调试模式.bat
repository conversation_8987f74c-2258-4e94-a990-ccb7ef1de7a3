@echo off
chcp 65001 >nul
echo ========================================
echo Freerouting 房间门模型可视化调试启动器
echo ========================================
echo.

:: 检查Java环境
echo [1/4] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境
    echo 请确保已安装Java 17或更高版本
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查项目目录
echo.
echo [2/4] 检查项目目录...
if not exist "build\libs\freerouting-executable.jar" (
    echo ❌ 错误: 未找到freerouting-executable.jar
    echo 请先编译项目: .\gradlew.bat build -x test
    pause
    exit /b 1
)
echo ✅ 项目文件检查通过

:: 检查测试文件
echo.
echo [3/4] 检查测试文件...
set TEST_FILE=
if exist "test_files\ultra_simple.dsn" (
    set TEST_FILE=test_files\ultra_simple.dsn
    echo ✅ 找到超简单测试文件: %TEST_FILE% （强烈推荐）
) else if exist "test_files\minimal_connection.dsn" (
    set TEST_FILE=test_files\minimal_connection.dsn
    echo ✅ 找到简单测试文件: %TEST_FILE%
) else if exist "test_files\simple_two_pins.dsn" (
    set TEST_FILE=test_files\simple_two_pins.dsn
    echo ✅ 找到测试文件: %TEST_FILE%
) else if exist "tests\Issue006-LPC18XX_43XX_SCH.dsn" (
    set TEST_FILE=tests\Issue006-LPC18XX_43XX_SCH.dsn
    echo ✅ 找到测试文件: %TEST_FILE%
) else (
    echo ⚠️  未找到推荐的测试文件，请手动加载PCB文件
)

:: 启动应用程序
echo.
echo [4/4] 启动Freerouting...
echo.
echo 📋 使用说明:
echo 1. 加载PCB文件 (File → Open)
if defined TEST_FILE (
    echo    推荐使用: %TEST_FILE%
)
echo 2. 点击菜单: 显示 → 房间门模型调试
echo 3. 按 'n' 键展开下一步，按 ESC 退出
echo 4. 观察不同颜色的房间和门结构
echo.
echo 🎨 增强可视化元素 (带清晰边界):
echo    🟢 明亮绿色 + 双层边界 + 白色R编号 = 自由空间房间
echo    🔴 明亮红色 + 双层边界 + 白色O编号 = 障碍物房间
echo    🟣 明亮紫色 + 双层边界 = 钻孔页面 (过孔网格)
echo    🔵 明亮蓝色 + 双层边界 + 白色V标识 = 过孔
echo    🔵 蓝色线条 + 双层边界 + D1标识 = 1维门
echo    🟡 黄色区域 + 双层边界 + D2标识 = 2维门
echo    🟠 橙色区域 + 清晰边界 + TS/TD标识 = 目标门
echo.
echo 正在启动应用程序...
echo ========================================

:: 启动freerouting
java -jar build\libs\freerouting-executable.jar

echo.
echo 应用程序已退出
pause
