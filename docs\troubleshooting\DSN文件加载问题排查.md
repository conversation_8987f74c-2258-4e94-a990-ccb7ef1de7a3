# DSN文件加载问题排查指南

## 🎯 概述

如果你遇到DSN文件无法正常加载或显示的问题，本指南将帮助你快速定位和解决问题。

## 🔍 常见问题和解决方案

### 问题1: 文件加载失败

#### 症状
- 点击"File → Open"后选择DSN文件，但没有任何反应
- 出现错误消息："无法加载文件"
- 程序崩溃或卡死

#### 解决方案
1. **检查文件格式**：
   ```
   ✅ 正确格式：(pcb filename.dsn ...)
   ❌ 错误格式：(PCB filename.dsn ...)  # 大小写敏感
   ```

2. **检查文件路径**：
   - 确保文件路径中没有中文字符
   - 避免使用空格和特殊字符
   - 推荐使用英文路径

3. **检查文件完整性**：
   - 确保文件没有被截断
   - 检查括号是否匹配
   - 验证文件编码为UTF-8

### 问题2: 文件加载成功但无内容显示

#### 症状
- 文件加载成功，但画布上什么都看不到
- 状态栏显示"已加载"但画布为空

#### 解决方案
1. **缩放到适合窗口**：
   - 按 **F** 键或点击工具栏的"适合窗口"按钮
   - 使用鼠标滚轮放大缩小

2. **检查层可见性**：
   - 点击菜单"显示" → "层可见性"
   - 确保相关层已启用显示

3. **检查对象可见性**：
   - 点击菜单"显示" → "对象可见性"
   - 确保引脚、走线等对象已启用显示

### 问题3: 房间门可视化无效果

#### 症状
- DSN文件正常显示，但启用房间门调试模式后看不到任何颜色

#### 解决方案
1. **确认文件包含未连接网络**：
   ```dsn
   (network
     (net SIGNAL_NAME
       (pins PIN1-1 PIN2-1)  # 必须有未连接的引脚对
     )
   )
   ```

2. **检查网络定义**：
   - 确保网络中有至少2个引脚
   - 确保引脚名称正确匹配
   - 确保没有预先布线

3. **使用推荐的测试文件**：
   - `test_files\ultra_simple.dsn` - 最简单，强烈推荐
   - `test_files\minimal_connection.dsn` - 标准测试
   - `test_files\simple_two_pins.dsn` - 多连接测试

## 📋 DSN文件格式要求

### 必需的基本结构

```dsn
(pcb filename.dsn
  (parser
    (string_quote ")
    (space_in_quoted_tokens on)
    (host_cad "KiCad's Pcbnew")
    (host_version "8.0.1")
  )
  (resolution um 10)
  (unit um)
  (structure
    (layer F.Cu
      (type signal)
      (property (index 0))
    )
    (boundary
      (path pcb 0  x1 y1  x2 y2  x3 y3  x4 y4  x1 y1)
    )
    (via "Via[0-0]_800:400_um")
    (rule
      (width 500)
      (clearance 300)
    )
  )
  (placement
    # 元件放置
  )
  (library
    # 元件库定义
  )
  (network
    # 网络定义
  )
  (wiring
    # 已有布线（通常为空）
  )
)
```

### 关键格式要点

1. **坐标系统**：
   - freerouting使用右手坐标系
   - Y轴向上为正，但DSN文件中通常使用负Y值
   - 单位为微米（um）

2. **边界定义**：
   ```dsn
   (boundary
     (path pcb 0  右上X 右上Y  左上X 左上Y  左下X 左下Y  右下X 右下Y  右上X 右上Y)
   )
   ```

3. **网络定义**：
   ```dsn
   (network
     (net NET_NAME
       (pins COMPONENT1-PIN1 COMPONENT2-PIN2)
     )
   )
   ```

## 🧪 测试文件说明

### ultra_simple.dsn
```
尺寸：20mm x 15mm
元件：2个测试点
网络：1个连接
特点：最小化设计，最佳可视化效果
推荐：初学者首选
```

### minimal_connection.dsn
```
尺寸：30mm x 20mm
元件：2个圆形焊盘
网络：1个连接
特点：标准焊盘形状
推荐：理解焊盘影响
```

### simple_two_pins.dsn
```
尺寸：40mm x 25mm
元件：2个连接器
网络：2个连接
特点：多网络交互
推荐：进阶学习
```

## 🔧 调试技巧

### 1. 逐步验证
```
步骤1：加载文件 → 检查是否有错误消息
步骤2：缩放适合 → 检查是否能看到PCB轮廓
步骤3：检查层显示 → 确认相关层已启用
步骤4：启用房间门调试 → 观察是否有状态消息
步骤5：按'n'键 → 检查是否开始扩展
```

### 2. 日志检查
- 查看控制台输出
- 注意任何错误或警告消息
- 记录具体的错误信息

### 3. 文件对比
- 与工作的测试文件对比
- 检查格式差异
- 验证关键字段

## 🆘 获取帮助

### 收集信息
如果问题仍然存在，请收集以下信息：

1. **文件信息**：
   - DSN文件内容（前50行）
   - 文件大小和创建时间
   - 原始CAD软件信息

2. **错误信息**：
   - 完整的错误消息
   - 控制台输出
   - 操作步骤

3. **环境信息**：
   - Java版本
   - 操作系统
   - freerouting版本

### 常用解决方案

1. **重新生成DSN文件**：
   - 从原始CAD软件重新导出
   - 检查导出设置
   - 确保包含所有必要信息

2. **使用标准测试文件**：
   - 先用提供的测试文件验证功能
   - 确认环境配置正确
   - 再尝试自定义文件

3. **简化文件内容**：
   - 移除复杂的元件
   - 只保留基本的连接
   - 逐步增加复杂度

## 💡 最佳实践

### 文件创建建议
1. **从简单开始**：先创建只有2个引脚的简单设计
2. **标准命名**：使用英文名称，避免特殊字符
3. **验证格式**：使用文本编辑器检查文件格式
4. **测试加载**：创建后立即测试加载

### 调试建议
1. **使用测试文件**：先确认功能正常
2. **逐步排查**：从简单到复杂逐步测试
3. **记录问题**：详细记录问题现象和解决过程
4. **备份文件**：保留工作版本作为参考

---

**提示**：大多数DSN文件加载问题都是格式相关的。使用提供的测试文件可以快速验证系统功能是否正常。
