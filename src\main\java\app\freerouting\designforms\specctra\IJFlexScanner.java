package app.freerouting.designforms.specctra;

import java.io.IOException;

/**
 * Interface for scanner generated by jflex.
 */
public interface IJFlexScanner
{
  /* Returns the current scope identifier. */
  String get_scope_identifier();

  /* Sets the current scope identifier. */
  void set_scope_identifier(String identifier);

  /**
   * Reads the next token from the input file.
   */
  Object next_token() throws IOException;

  /**
   * Reads the next token as a string from the input file.
   */
  String next_string();

  String next_string(boolean ignoreNewline);

  String[] next_string_list();

  String[] next_string_list(char separator);

  /**
   * Reads the next token as a double from the input file.
   */
  Double next_double();

  Boolean next_closing_bracket();

  /**
   * Starts a new state.
   */
  void yybegin(int p_new_state);
}