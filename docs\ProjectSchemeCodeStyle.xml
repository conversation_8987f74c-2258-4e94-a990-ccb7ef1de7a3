<code_scheme name="Project" version="173">
  <JavaCodeStyleSettings>
    <option name="ALIGN_MULTILINE_ANNOTATION_PARAMETERS" value="true" />
    <option name="ALIGN_MULTILINE_TEXT_BLOCKS" value="true" />
  </JavaCodeStyleSettings>
  <codeStyleSettings language="JAVA">
    <option name="RIGHT_MARGIN" value="500" />
    <option name="KEEP_LINE_BREAKS" value="false" />
    <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
    <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
    <option name="BRACE_STYLE" value="2" />
    <option name="CLASS_BRACE_STYLE" value="2" />
    <option name="METHOD_BRACE_STYLE" value="2" />
    <option name="LAMBDA_BRACE_STYLE" value="2" />
    <option name="ELSE_ON_NEW_LINE" value="true" />
    <option name="ALIGN_MULTILINE_CHAINED_METHODS" value="true" />
    <option name="ALIGN_MULTILINE_ARRAY_INITIALIZER_EXPRESSION" value="true" />
    <option name="METHOD_CALL_CHAIN_WRAP" value="2" />
    <option name="WRAP_FIRST_METHOD_IN_CALL_CHAIN" value="true" />
    <option name="ARRAY_INITIALIZER_WRAP" value="2" />
    <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true" />
    <option name="ARRAY_INITIALIZER_RBRACE_ON_NEXT_LINE" value="true" />
    <option name="IF_BRACE_FORCE" value="3" />
    <option name="DOWHILE_BRACE_FORCE" value="3" />
    <option name="WHILE_BRACE_FORCE" value="3" />
    <option name="FOR_BRACE_FORCE" value="3" />
    <option name="PARAMETER_ANNOTATION_WRAP" value="2" />
    <option name="KEEP_BUILDER_METHODS_INDENTS" value="true" />
    <option name="WRAP_ON_TYPING" value="0" />
    <indentOptions>
      <option name="INDENT_SIZE" value="2" />
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
      <option name="TAB_SIZE" value="2" />
    </indentOptions>
  </codeStyleSettings>
</code_scheme>