# 房间门可视化快速测试指南

## 🚀 5分钟快速体验

### 步骤1: 编译和启动（2分钟）

```bash
# 方法1: 使用脚本（推荐）
scripts\编译房间门调试版本.bat
scripts\启动房间门调试模式.bat

# 方法2: 手动操作
.\gradlew.bat build -x test
java -jar build\libs\freerouting-executable.jar
```

### 步骤2: 加载测试文件（1分钟）

1. **推荐使用简单测试文件**：
   - 点击 **File** → **Open**
   - 选择 `test_files\ultra_simple.dsn`（最推荐）
   - 这个文件只有两个测试点，最适合观察房间门模型

2. **文件说明**：
   ```
   ultra_simple.dsn        - 超简单：两个测试点，一个连接（强烈推荐）
   minimal_connection.dsn  - 简单：两个圆形焊盘，一个连接
   simple_two_pins.dsn     - 中等：两个连接器，两个连接
   ```

### 步骤3: 启用可视化（30秒）

1. 点击菜单 **"显示"** → **"房间门模型调试"**
2. 状态栏显示：`房间门模型调试模式已启用 - 按'n'展开下一步，按ESC退出`

### 步骤4: 观察房间门生成（1.5分钟）

1. **自动开始**：系统会在电路板中心开始扩展
2. **手动控制**：
   - 按 **'n'** 键：展开下一个房间
   - 按 **ESC** 键：退出调试模式

## 🎨 你将看到的效果

### 增强的可视化效果

| 元素 | 颜色 | 说明 | 何时出现 |
|------|------|------|----------|
| **自由空间房间** | 🟢 明亮绿色 + 清晰边界 + 白色R编号 | 可以布线的区域 | 按'n'键逐步出现 |
| **障碍物房间** | 🔴 明亮红色 + 清晰边界 + 白色O编号 | 被焊盘占用的区域 | 围绕焊盘出现 |
| **钻孔页面** | 🟣 明亮紫色 + 双层边界 | 过孔候选网格 | 覆盖整个区域 |
| **过孔** | 🔵 明亮蓝色 + 双层边界 + 白色V标识 | 过孔候选位置 | 钻孔页面内出现 |
| **1维门** | 🔵 蓝色线条 + 双层边界 + D1标识 | 线段形式的房间连接 | 房间之间出现 |
| **2维门** | 🟡 黄色区域 + 双层边界 + D2标识 | 区域形式的房间连接 | 重叠房间间出现 |
| **目标门** | 🟠 橙色区域 + 清晰边界 + TS/TD标识 | 连接到PCB对象的门 | 房间与焊盘间 |
| **清晰边界** | 外层粗线 + 内层细线 | 所有元素的精确边界 | 增强可见性 |

### 典型的观察序列

```
第1步: 按'n' → 出现紫色钻孔页面网格，覆盖整个PCB
第2步: 按'n' → 在起始点出现小的绿色房间R1
第3步: 按'n' → 绿色房间开始扩展，编号递增R2, R3...
第4步: 按'n' → 遇到焊盘，出现红色障碍物房间O1, O2...
第5步: 按'n' → 房间之间出现蓝色门D1（1维）或黄色门D2（2维）
第6步: 按'n' → 房间与焊盘间出现橙色目标门TS/TD
第7步: 按'n' → 钻孔页面内出现蓝色过孔V
...继续按'n'观察完整的房间门网络形成过程
```

## 🔍 观察要点

### 1. 房间扩展模式
- **起始扩展**：从中心点开始的小圆形房间
- **边界适应**：房间如何适应PCB边界
- **障碍物避让**：房间如何绕过焊盘和走线

### 2. 颜色变化
- **绿色强度**：房间越新，绿色越亮
- **红色区域**：每个PCB对象周围的禁止区域
- **紫色网格**：过孔可以放置的位置

### 3. 几何形状
- **房间形状**：通常是多边形，适应周围环境
- **边界精度**：注意房间边界的精确性
- **连接关系**：观察房间之间如何连接

## 🎯 测试场景

### 场景1: 超简单连接（ultra_simple.dsn）
```
目标：观察最基本的房间门模型
期望：看到两个测试点之间的房间扩展过程
重点：理解房间如何从一个点扩展到另一个点
特点：20mm x 15mm小尺寸，最清晰的可视化效果
```

### 场景2: 简单连接（minimal_connection.dsn）
```
目标：观察标准焊盘的房间生成
期望：看到圆形焊盘周围的房间形成
重点：理解焊盘形状对房间的影响
```

### 场景3: 多连接（simple_two_pins.dsn）
```
目标：观察多个网络的房间生成
期望：看到两个独立的房间系统
重点：理解不同网络的房间如何相互避让
```

### 场景3: 手动选择位置
```
操作：在调试模式下点击不同位置
目标：观察不同起始位置的房间扩展
重点：理解算法对起始位置的敏感性
```

## 🐛 常见问题快速解决

### 问题1: 看不到任何颜色
**解决**：
1. 确认已点击"房间门模型调试"菜单
2. 按'n'键开始扩展
3. 检查是否加载了正确的PCB文件

### 问题2: 颜色太淡看不清
**解决**：
- 新版本已增强颜色对比度
- 绿色房间现在是60%不透明
- 红色障碍物是50%不透明

### 问题3: 扩展过程太快
**解决**：
- 使用'n'键手动控制每一步
- 每按一次'n'只扩展一个房间
- 可以随时按ESC退出

### 问题4: 程序无响应
**解决**：
1. 按ESC键退出调试模式
2. 重新加载PCB文件
3. 重新启动程序

## 📊 性能提示

### 最佳测试环境
- **文件大小**：建议使用小型PCB文件（<100个对象）
- **内存使用**：调试模式会增加内存使用
- **显示性能**：现代显卡获得更好的渲染效果

### 优化建议
- **逐步观察**：不要连续快速按'n'键
- **适时退出**：观察完成后及时退出调试模式
- **文件选择**：从简单文件开始，逐步尝试复杂文件

## 🎓 学习建议

### 第一次使用
1. 使用 `minimal_connection.dsn`
2. 慢慢按'n'键，观察每一步
3. 重点观察绿色房间的扩展过程
4. 理解红色障碍物的作用

### 进阶使用
1. 尝试 `simple_two_pins.dsn`
2. 观察多个网络的相互作用
3. 尝试在不同位置点击开始扩展
4. 分析算法的效率和准确性

### 专家级使用
1. 加载复杂的实际PCB文件
2. 分析算法在实际场景中的表现
3. 观察性能瓶颈和优化机会
4. 考虑算法改进的可能性

## 📝 记录观察结果

建议在测试过程中记录：
- 房间扩展的步骤数
- 遇到的障碍物类型
- 算法的执行时间
- 生成的房间数量
- 发现的有趣现象

这些记录将帮助你更深入地理解freerouting的房间门模型算法！

---

**提示**：这个可视化功能是学习和研究PCB自动布线算法的绝佳工具。通过观察房间门模型的生成过程，你可以直观地理解现代自动布线算法的工作原理。
