# Freerouting 布线算法详细解析

## 1. 布线算法总体架构

### 1.1 布线流程概览

```mermaid
graph TD
    A[开始布线] --> B[初始化布线引擎]
    B --> C[扇出阶段]
    C --> D[批量布线阶段]
    D --> E[后处理优化]
    E --> F[完成布线]
    
    C --> C1[SMD引脚扇出]
    C --> C2[创建过孔连接点]
    
    D --> D1[网络优先级排序]
    D --> D2[逐个连接布线]
    D --> D3[冲突处理]
    
    D2 --> D2a[迷宫搜索]
    D2 --> D2b[路径回溯]
    D2 --> D2c[几何计算]
    D2 --> D2d[走线插入]
    
    E --> E1[Pull-tight优化]
    E --> E2[过孔优化]
    E --> E3[DRC检查]
```

### 1.2 核心组件关系

```java
AutorouteEngine (布线引擎)
├── MazeSearchAlgo (迷宫搜索算法)
│   ├── 房间-门模型管理
│   ├── 优先队列扩展
│   └── 目标搜索
├── LocateFoundConnectionAlgo (路径定位算法)
│   ├── 回溯数组构建
│   ├── 几何角点计算
│   └── 角度约束处理
└── InsertFoundConnectionAlgo (走线插入算法)
    ├── 推挤算法
    ├── 撕裂算法
    └── 过孔插入
```

## 2. 扇出阶段详解

### 2.1 扇出算法流程

```java
// BatchFanout.java
public void runBatchLoop()
{
    for (int curr_pass_no = 0; curr_pass_no < this.settings.maxPasses; ++curr_pass_no)
    {
        int routed_count = this.fanout_pass(curr_pass_no);
        if (routed_count == 0) {
            break; // 没有新的扇出，结束
        }
    }
}

private int fanout_pass(int p_pass_no)
{
    int routed_count = 0;
    int ripup_costs = settings.get_start_ripup_costs() * (p_pass_no + 1);
    
    // 按组件排序处理
    for (Component curr_component : this.sorted_components)
    {
        // 处理每个SMD引脚
        for (Component.Pin curr_pin : curr_component.smd_pins)
        {
            TimeLimit time_limit = new TimeLimit(10000 * (p_pass_no + 1));
            AutorouteAttemptResult curr_result = this.board.fanout(
                curr_pin.board_pin, settings, ripup_costs, this.thread, time_limit);
            
            if (curr_result.state == AutorouteAttemptState.ROUTED) {
                ++routed_count;
            }
        }
    }
    return routed_count;
}
```

### 2.2 扇出目标和策略

**扇出目标**：
1. 将SMD引脚从表面层连接到内层
2. 创建过孔作为后续布线的连接点
3. 避免引脚密集区域的拥塞

**扇出策略**：
- **组件优先级**: 引脚密度高的组件优先处理
- **方向选择**: 选择阻抗最小的扇出方向
- **过孔位置**: 在合适位置插入过孔连接层间

## 3. 批量布线阶段

### 3.1 批量布线主循环

```java
// BatchAutorouter.java
public boolean autoroute_pass(int p_pass_no)
{
    boolean something_changed = false;
    
    // 获取所有需要布线的对象
    Collection<Item> autoroute_item_list = this.board.get_connectable_items();
    
    for (Item curr_item : autoroute_item_list)
    {
        if (curr_item.net_count() == 0) continue;
        
        // 处理每个网络
        for (int i = 0; i < curr_item.net_count(); ++i)
        {
            // 检查停止请求
            if (this.thread.is_stop_auto_router_requested()) {
                this.is_interrupted = true;
                break;
            }
            
            // 标记变化区域
            board.start_marking_changed_area();
            
            // 执行单个对象的布线
            SortedSet<Item> ripped_item_list = new TreeSet<>();
            boolean useSlowAlgorithm = p_pass_no % 4 == 0;
            
            var autorouterResult = autoroute_item(curr_item, curr_item.get_net_no(i), 
                                                 ripped_item_list, p_pass_no, useSlowAlgorithm);
            
            if (autorouterResult.state == AutorouteAttemptState.ROUTED) {
                something_changed = true;
            }
        }
    }
    
    return something_changed;
}
```

### 3.2 单个连接布线流程

```java
// AutorouteEngine.java
public AutorouteAttemptResult autoroute_connection(Set<Item> p_start_set, 
                                                  Set<Item> p_dest_set, 
                                                  AutorouteControl p_ctrl, 
                                                  SortedSet<Item> p_ripped_item_list)
{
    // 1. 创建迷宫搜索算法实例
    MazeSearchAlgo maze_search_algo = MazeSearchAlgo.get_instance(
        p_start_set, p_dest_set, this, p_ctrl);
    
    if (maze_search_algo == null) {
        return new AutorouteAttemptResult(AutorouteAttemptState.FAILED);
    }
    
    // 2. 执行迷宫搜索
    MazeSearchAlgo.Result search_result = maze_search_algo.find_connection();
    
    if (search_result == null) {
        return new AutorouteAttemptResult(AutorouteAttemptState.FAILED);
    }
    
    // 3. 路径回溯和几何计算
    LocateFoundConnectionAlgo autoroute_result = LocateFoundConnectionAlgo.get_instance(
        search_result, p_ctrl, this.autoroute_search_tree, 
        board.rules.get_trace_angle_restriction(), p_ripped_item_list);
    
    if (autoroute_result == null) {
        return new AutorouteAttemptResult(AutorouteAttemptState.FAILED);
    }
    
    // 4. 插入走线
    InsertFoundConnectionAlgo.Result insert_result = InsertFoundConnectionAlgo.insert_connection(
        autoroute_result, this.board, p_ctrl);
    
    if (insert_result == null) {
        return new AutorouteAttemptResult(AutorouteAttemptState.INSERT_ERROR);
    }
    
    return new AutorouteAttemptResult(AutorouteAttemptState.ROUTED);
}
```

## 4. 迷宫搜索算法详解

### 4.1 算法初始化

```java
// MazeSearchAlgo.java
private boolean init(Set<Item> p_start_items, Set<Item> p_destination_items)
{
    // 1. 处理目标对象
    for (Item curr_item : p_destination_items) {
        ItemAutorouteInfo curr_info = curr_item.get_autoroute_info();
        curr_info.set_destination_info(true);
        
        // 创建目标门
        if (curr_item instanceof Connectable) {
            for (int i = 0; i < curr_item.tree_shape_count(search_tree); ++i) {
                TileShape connection_shape = ((Connectable) curr_item)
                    .get_trace_connection_shape(search_tree, i);
                TargetItemExpansionDoor target_door = new TargetItemExpansionDoor(
                    curr_item, i, autoroute_engine);
                target_door.set_destination_door(true);
            }
        }
    }
    
    // 2. 处理起始对象，创建起始房间
    Collection<IncompleteFreeSpaceExpansionRoom> start_rooms = new LinkedList<>();
    for (Item curr_item : p_start_items) {
        ItemAutorouteInfo curr_info = curr_item.get_autoroute_info();
        curr_info.set_start_info(true);
        
        if (curr_item instanceof Connectable) {
            for (int i = 0; i < curr_item.tree_shape_count(search_tree); ++i) {
                TileShape contained_shape = ((Connectable) curr_item)
                    .get_trace_connection_shape(search_tree, i);
                IncompleteFreeSpaceExpansionRoom new_start_room = 
                    autoroute_engine.add_incomplete_expansion_room(
                        null, curr_item.shape_layer(i), contained_shape);
                start_rooms.add(new_start_room);
            }
        }
    }
    
    // 3. 完成起始房间并初始化扩展列表
    for (IncompleteFreeSpaceExpansionRoom curr_room : start_rooms) {
        Collection<CompleteFreeSpaceExpansionRoom> curr_completed_rooms = 
            autoroute_engine.complete_expansion_room(curr_room);
        
        for (CompleteFreeSpaceExpansionRoom completed_room : curr_completed_rooms) {
            add_to_expansion_list(completed_room);
        }
    }
    
    return true;
}
```

### 4.2 迷宫搜索主循环

```java
public Result find_connection()
{
    while (occupy_next_element()) {
        // 持续扩展直到找到目标或扩展列表为空
    }
    
    if (this.destination_door == null) {
        return null; // 未找到连接
    }
    return new Result(this.destination_door, this.section_no_of_destination_door);
}

public boolean occupy_next_element()
{
    if (this.destination_door != null) {
        return false; // 目标已找到
    }
    
    // 1. 从优先队列中取出下一个扩展元素
    MazeListElement list_element = null;
    MazeSearchElement curr_door_section = null;
    
    while (!maze_expansion_list.isEmpty()) {
        if (this.autoroute_engine.is_stop_requested()) {
            return false;
        }
        
        Iterator<MazeListElement> it = maze_expansion_list.iterator();
        list_element = it.next();
        int curr_section_no = list_element.section_no_of_door;
        curr_door_section = list_element.door.get_maze_search_element(curr_section_no);
        it.remove();
        
        if (!curr_door_section.is_occupied) {
            break; // 找到未占用的元素
        }
    }
    
    if (list_element == null) {
        return false; // 扩展列表为空
    }
    
    // 2. 设置回溯信息
    curr_door_section.backtrack_door = list_element.backtrack_door;
    curr_door_section.section_no_of_backtrack_door = list_element.section_no_of_backtrack_door;
    curr_door_section.room_ripped = list_element.room_ripped;
    curr_door_section.adjustment = list_element.adjustment;
    
    // 3. 检查是否到达目标
    if (list_element.door instanceof TargetItemExpansionDoor curr_door) {
        if (curr_door.is_destination_door()) {
            this.destination_door = curr_door;
            this.section_no_of_destination_door = list_element.section_no_of_door;
            return false; // 找到目标
        }
    }
    
    // 4. 扩展到其他层（过孔）
    if (ctrl.vias_allowed && list_element.door instanceof ExpansionDrill && 
        !(list_element.backtrack_door instanceof ExpansionDrill)) {
        expand_to_other_layers(list_element);
    }
    
    // 5. 扩展到房间的其他门
    if (list_element.next_room != null) {
        if (!expand_to_room_doors(list_element)) {
            return true; // 推挤延迟或无扩展
        }
    }
    
    curr_door_section.is_occupied = true;
    return true;
}
```

### 4.3 房间扩展算法

```java
private boolean expand_to_room_doors(MazeListElement p_list_element)
{
    int layer_no = p_list_element.next_room.get_layer();
    boolean layer_active = ctrl.layer_active[layer_no];

    // 1. 检查层是否激活
    if (!layer_active && autoroute_engine.board.layer_structure.arr[layer_no].is_signal) {
        return true;
    }

    // 2. 完成邻居房间
    CompleteExpansionRoom completed_room = autoroute_engine.complete_expansion_room(p_list_element.next_room);
    if (completed_room == null) {
        return true;
    }

    // 3. 检查房间类型和处理策略
    boolean something_expanded = false;
    int ripup_costs = 0;

    if (p_list_element.next_room instanceof FreeSpaceExpansionRoom) {
        // 自由空间房间：直接扩展
        something_expanded = expand_to_target_doors(p_list_element, false, false, null);
    }
    else if (p_list_element.next_room instanceof ObstacleExpansionRoom obstacle_room) {
        // 障碍物房间：检查撕裂可能性
        if (this.ctrl.ripup_allowed) {
            ripup_costs = check_ripup(p_list_element, obstacle_room.get_item(), false);
            boolean room_rippable = (ripup_costs >= 0);

            if (room_rippable) {
                // 可以撕裂：推挤或撕裂处理
                if (obstacle_room.get_item() instanceof PolylineTrace) {
                    something_expanded = shove_trace_room(p_list_element, obstacle_room);
                } else {
                    something_expanded = expand_to_target_doors(p_list_element, false, false, null);
                }
            }
        }
    }

    // 4. 扩展到钻孔页面（过孔候选位置）
    if (ctrl.vias_allowed && !(p_list_element.door instanceof ExpansionDrill)) {
        if (something_expanded && p_list_element.next_room instanceof CompleteFreeSpaceExpansionRoom) {
            Collection<DrillPage> overlapping_drill_pages =
                this.autoroute_engine.drill_page_array.overlapping_pages(
                    p_list_element.next_room.get_shape());

            for (DrillPage to_drill_page : overlapping_drill_pages) {
                expand_to_drill_page(to_drill_page, p_list_element);
            }
        }
    }

    return something_expanded;
}
```

### 4.4 过孔扩展算法

```java
private void expand_to_other_layers(MazeListElement p_list_element)
{
    ExpansionDrill curr_drill = (ExpansionDrill) p_list_element.door;
    int from_layer = curr_drill.first_layer + p_list_element.section_no_of_door;

    // 1. 计算过孔的可行层范围
    int via_lower_bound = Math.max(0, ctrl.via_lower_bound);
    int via_upper_bound = Math.min(autoroute_engine.board.get_layer_count() - 1, ctrl.via_upper_bound);

    // 向下搜索可行层
    int curr_layer = from_layer - 1;
    while (curr_layer >= via_lower_bound) {
        TileShape curr_room_shape = curr_drill.room_arr[curr_layer - curr_drill.first_layer].get_shape();

        ForcedPadAlgo.CheckDrillResult drill_result = ForcedViaAlgo.check_layer(
            ctrl.via_radius_arr[curr_layer],
            ctrl.via_clearance_class,
            ctrl.attach_smd_allowed,
            curr_room_shape,
            curr_drill.location,
            curr_layer,
            net_no_arr,
            ctrl.max_shove_trace_recursion_depth,
            0,
            autoroute_engine.board);

        if (drill_result == ForcedPadAlgo.CheckDrillResult.NOT_DRILLABLE) {
            via_lower_bound = curr_layer + 1;
            break;
        }
        --curr_layer;
    }

    // 向上搜索可行层（类似逻辑）
    curr_layer = from_layer + 1;
    while (curr_layer <= via_upper_bound) {
        // 检查可钻孔性...
        ++curr_layer;
    }

    // 2. 在可行层范围内创建过孔扩展
    int layer_change_cost = ctrl.layer_change_costs[from_layer];
    for (int layer = via_lower_bound; layer <= via_upper_bound; ++layer) {
        if (layer != from_layer) {
            expand_to_drill(curr_drill, p_list_element, layer_change_cost, layer);
        }
    }
}
```

## 5. 路径回溯与几何计算

### 5.1 回溯数组构建

```java
// LocateFoundConnectionAlgo.java
private static Collection<BacktrackElement> backtrack(MazeSearchAlgo.Result p_maze_search_result,
                                                     SortedSet<Item> p_ripped_item_list)
{
    Collection<BacktrackElement> result = new LinkedList<>();
    ExpandableObject curr_backtrack_door = p_maze_search_result.door;
    MazeSearchElement curr_maze_search_element =
        curr_backtrack_door.get_maze_search_element(p_maze_search_result.section_no_of_door);
    CompleteExpansionRoom curr_next_room =
        curr_backtrack_door.other_room_complete(curr_maze_search_element.backtrack_door);

    BacktrackElement curr_backtrack_element = new BacktrackElement(
        curr_backtrack_door, p_maze_search_result.section_no_of_door, curr_next_room);

    // 沿着回溯链追溯到起点
    for (;;) {
        result.add(curr_backtrack_element);
        curr_backtrack_door = curr_maze_search_element.backtrack_door;

        if (curr_backtrack_door == null) {
            break; // 到达起点
        }

        int curr_section_no = curr_maze_search_element.section_no_of_backtrack_door;
        curr_maze_search_element = curr_backtrack_door.get_maze_search_element(curr_section_no);

        // 处理过孔门
        if (curr_backtrack_door instanceof ExpansionDrill curr_drill) {
            curr_next_room = curr_drill.room_arr[curr_section_no];
        } else {
            curr_next_room = curr_backtrack_door.other_room_complete(curr_maze_search_element.backtrack_door);
        }

        curr_backtrack_element = new BacktrackElement(curr_backtrack_door, curr_section_no, curr_next_room);

        // 收集撕裂的对象
        if (curr_maze_search_element.room_ripped && curr_next_room instanceof ObstacleExpansionRoom) {
            p_ripped_item_list.add(((ObstacleExpansionRoom) curr_next_room).get_item());
        }
    }

    return result;
}
```

### 5.2 45度角点计算

```java
// LocateFoundConnectionAlgo45Degree.java
@Override
protected Collection<FloatPoint> calculate_next_trace_corners()
{
    Collection<FloatPoint> result = new LinkedList<>();

    if (this.current_to_door_index > this.current_target_door_index) {
        return result; // 已完成
    }

    BacktrackElement curr_from_info = this.backtrack_array[this.current_to_door_index - 1];

    if (curr_from_info.next_room == null) {
        return result;
    }

    // 1. 获取房间形状并收缩以考虑走线宽度
    TileShape room_shape = curr_from_info.next_room.get_shape();
    int trace_halfwidth = this.ctrl.compensated_trace_half_width[this.current_trace_layer];
    int shrink_offset = trace_halfwidth + AutorouteEngine.TRACE_WIDTH_TOLERANCE;

    TileShape shrinked_room_shape = (TileShape) room_shape.offset(-shrink_offset);

    if (!shrinked_room_shape.is_empty()) {
        // 2. 进入收缩后的房间
        FloatPoint nearest_room_point = shrinked_room_shape.nearest_point_approx(this.current_from_point);
        boolean horizontal_first = calc_horizontal_first_from_door(
            curr_from_info.door, this.current_from_point, nearest_room_point);
        nearest_room_point = round_to_integer(nearest_room_point);

        // 3. 添加45度角点
        FloatPoint additional_corner = calculate_additional_corner(
            this.current_from_point, nearest_room_point, horizontal_first, this.angle_restriction);

        if (!additional_corner.equals(this.current_from_point)) {
            result.add(additional_corner);
        }
        result.add(nearest_room_point);
        this.current_from_point = nearest_room_point;
    }

    // 4. 检查是否到达目标
    if (this.current_to_door_index == this.current_target_door_index) {
        FloatPoint nearest_point = this.current_target_shape.nearest_point_approx(this.current_from_point);
        nearest_point = round_to_integer(nearest_point);

        FloatPoint add_corner = calculate_additional_corner(
            this.current_from_point, nearest_point, true, this.angle_restriction);

        if (!shrinked_room_shape.contains(add_corner)) {
            add_corner = calculate_additional_corner(
                this.current_from_point, nearest_point, false, this.angle_restriction);
        }

        if (!add_corner.equals(this.current_from_point)) {
            result.add(add_corner);
        }
        result.add(nearest_point);
        ++this.current_to_door_index;
        return result;
    }

    // 5. 计算到下一个门的路径
    BacktrackElement curr_to_info = this.backtrack_array[this.current_to_door_index];
    if (!(curr_to_info.door instanceof ExpansionDoor curr_to_door)) {
        return result;
    }

    FloatPoint nearest_to_door_point;
    if (curr_to_door.dimension == 2) {
        // 2维门：面积门
        TileShape to_door_shape = curr_to_door.get_shape();
        TileShape shrinked_to_door_shape = (TileShape) to_door_shape.shrink(shrink_offset);
        nearest_to_door_point = shrinked_to_door_shape.nearest_point_approx(this.current_from_point);
    } else {
        // 1维门：线段门
        FloatLine[] line_sections = curr_to_door.get_section_segments(trace_halfwidth);
        if (curr_to_info.section_no_of_door < line_sections.length) {
            FloatLine curr_line_section = line_sections[curr_to_info.section_no_of_door];
            nearest_to_door_point = curr_line_section.nearest_segment_point(this.current_from_point);
        } else {
            nearest_to_door_point = this.current_from_point;
        }
    }

    nearest_to_door_point = round_to_integer(nearest_to_door_point);
    boolean horizontal_first = calc_horizontal_first_to_door(
        curr_to_info.door, this.current_from_point, nearest_to_door_point);

    FloatPoint additional_corner = calculate_additional_corner(
        this.current_from_point, nearest_to_door_point, horizontal_first, this.angle_restriction);

    if (!additional_corner.equals(this.current_from_point)) {
        result.add(additional_corner);
    }
    result.add(nearest_to_door_point);
    ++this.current_to_door_index;

    return result;
}
```

### 5.3 45度角点计算核心算法

```java
// LocateFoundConnectionAlgo.java
static FloatPoint calculate_additional_corner(FloatPoint p_from_point,
                                            FloatPoint p_to_point,
                                            boolean p_horizontal_first,
                                            AngleRestriction p_angle_restriction)
{
    FloatPoint result;
    if (p_angle_restriction == AngleRestriction.NINETY_DEGREE) {
        result = ninety_degree_corner(p_from_point, p_to_point, p_horizontal_first);
    } else if (p_angle_restriction == AngleRestriction.FORTYFIVE_DEGREE) {
        result = fortyfive_degree_corner(p_from_point, p_to_point, p_horizontal_first);
    } else {
        result = p_to_point; // 自由角度
    }
    return result;
}

private static FloatPoint fortyfive_degree_corner(FloatPoint p_from_point,
                                                FloatPoint p_to_point,
                                                boolean p_horizontal_first)
{
    double abs_dx = Math.abs(p_to_point.x - p_from_point.x);
    double abs_dy = Math.abs(p_to_point.y - p_from_point.y);
    double x, y;

    if (abs_dx <= abs_dy) {
        if (p_horizontal_first) {
            // 水平优先：先水平移动，再45度
            x = p_to_point.x;
            if (p_to_point.y >= p_from_point.y) {
                y = p_from_point.y + abs_dx;
            } else {
                y = p_from_point.y - abs_dx;
            }
        } else {
            // 垂直优先：先垂直移动，再45度
            x = p_from_point.x;
            if (p_to_point.y > p_from_point.y) {
                y = p_to_point.y - abs_dx;
            } else {
                y = p_to_point.y + abs_dx;
            }
        }
    } else {
        // abs_dx > abs_dy
        if (p_horizontal_first) {
            x = p_from_point.x;
            if (p_to_point.x > p_from_point.x) {
                x = p_to_point.x - abs_dy;
            } else {
                x = p_to_point.x + abs_dy;
            }
            y = p_to_point.y;
        } else {
            x = p_to_point.x;
            y = p_from_point.y;
            if (p_to_point.y > p_from_point.y) {
                y = p_from_point.y + abs_dy;
            } else {
                y = p_from_point.y - abs_dy;
            }
        }
    }

    return new FloatPoint(x, y);
}
```

## 6. 走线插入算法

### 6.1 强制插入主流程

```java
// InsertFoundConnectionAlgo.java
public static Result insert_connection(LocateFoundConnectionAlgo.Result p_connection,
                                     RoutingBoard p_board,
                                     AutorouteControl p_ctrl)
{
    int curr_layer = p_connection.target_layer;
    InsertFoundConnectionAlgo new_instance = new InsertFoundConnectionAlgo(p_board, p_ctrl);

    // 逐段插入走线
    for (LocateFoundConnectionAlgoAnyAngle.ResultItem curr_new_item : p_connection.connection_items) {
        // 1. 插入层间过孔
        if (!new_instance.insert_via(curr_new_item.corners[0], curr_layer, curr_new_item.layer)) {
            return null;
        }
        curr_layer = curr_new_item.layer;

        // 2. 插入走线段
        if (!new_instance.insert_trace(curr_new_item)) {
            return null;
        }
    }

    // 3. 插入最终过孔
    if (!new_instance.insert_via(new_instance.last_corner, curr_layer, p_connection.start_layer)) {
        return null;
    }

    return new Result();
}
```

### 6.2 推挤算法实现

```java
private boolean insert_trace(LocateFoundConnectionAlgoAnyAngle.ResultItem p_trace)
{
    if (p_trace.corners.length == 1) {
        // 单点走线
        if (this.first_corner == null) {
            this.first_corner = p_trace.corners[0];
        }
        this.last_corner = p_trace.corners[0];
        return true;
    }

    int[] net_no_arr = new int[1];
    net_no_arr[0] = ctrl.net_no;

    // 逐段插入走线
    int from_corner_no = 0;
    for (int i = 1; i < p_trace.corners.length; ++i) {
        Point[] curr_corner_arr = Arrays.copyOfRange(p_trace.corners, from_corner_no, i + 1);
        Polyline insert_polyline = new Polyline(curr_corner_arr);

        // 强制插入走线段，使用推挤算法
        Point ok_point = board.insert_forced_trace_polyline(
            insert_polyline,
            ctrl.trace_half_width[p_trace.layer],
            p_trace.layer,
            net_no_arr,
            ctrl.trace_clearance_class_no,
            ctrl.max_shove_trace_recursion_depth,    // 推挤递归深度
            ctrl.max_shove_via_recursion_depth,
            ctrl.max_spring_over_recursion_depth,    // 跨越递归深度
            Integer.MAX_VALUE,
            ctrl.pull_tight_accuracy,
            true,
            null
        );

        if (ok_point == insert_polyline.first_corner()) {
            return false; // 插入失败
        }

        from_corner_no = i - 1;
        if (this.first_corner == null) {
            this.first_corner = insert_polyline.first_corner();
        }
        this.last_corner = ok_point;
    }

    return true;
}
```

### 6.3 推挤算法详细实现

```java
// ShoveTraceAlgo.java
public boolean insert(TileShape p_trace_shape, CalcFromSide p_from_side,
                     int p_layer, int[] p_net_no_arr, int p_cl_type,
                     Collection<Item> p_ignore_items,
                     int p_max_recursion_depth, int p_max_via_recursion_depth,
                     int p_max_spring_over_recursion_depth)
{
    if (p_trace_shape.is_empty()) {
        return true;
    }

    if (!p_trace_shape.is_contained_in(board.get_bounding_box())) {
        this.board.set_shove_failing_obstacle(board.get_outline());
        return false;
    }

    // 1. 查找重叠的对象
    ShapeTraceEntries shape_entries = new ShapeTraceEntries(p_trace_shape, p_layer,
                                                           p_net_no_arr, p_cl_type,
                                                           p_ignore_items, board);

    if (shape_entries.substitute_trace_count() > 0) {
        // 有需要替换的走线
        return insert_with_substitute_traces(shape_entries, p_from_side, p_layer,
                                           p_net_no_arr, p_cl_type, p_ignore_items,
                                           p_max_recursion_depth, p_max_via_recursion_depth,
                                           p_max_spring_over_recursion_depth);
    }

    // 2. 检查推挤可行性
    if (shape_entries.stack_depth() > 1) {
        this.board.set_shove_failing_obstacle(shape_entries.get_found_obstacle());
        return false;
    }

    // 3. 推挤过孔
    for (Via curr_shove_via : shape_entries.shove_via_list) {
        if (curr_shove_via.shares_net_no(p_net_no_arr)) {
            continue; // 同网络过孔不需要推挤
        }

        if (p_max_via_recursion_depth <= 0) {
            this.board.set_shove_failing_obstacle(curr_shove_via);
            return false;
        }

        // 计算过孔推挤位置
        IntPoint[] try_via_centers = MoveDrillItemAlgo.try_shove_via_points(
            p_trace_shape, p_layer, curr_shove_via, p_cl_type, true, board);

        boolean via_shove_ok = false;
        for (IntPoint try_via_center : try_via_centers) {
            if (board.move_drill_item(curr_shove_via, try_via_center,
                                    p_max_via_recursion_depth - 1)) {
                via_shove_ok = true;
                break;
            }
        }

        if (!via_shove_ok) {
            this.board.set_shove_failing_obstacle(curr_shove_via);
            return false;
        }
    }

    // 4. 推挤走线
    for (PolylineTrace curr_shove_trace : shape_entries.shove_trace_list) {
        if (curr_shove_trace.shares_net_no(p_net_no_arr)) {
            continue; // 同网络走线不需要推挤
        }

        if (p_max_recursion_depth <= 0) {
            this.board.set_shove_failing_obstacle(curr_shove_trace);
            return false;
        }

        // 推挤走线
        if (!shove_trace(curr_shove_trace, p_trace_shape, p_from_side, p_layer,
                        p_net_no_arr, p_cl_type, p_ignore_items,
                        p_max_recursion_depth - 1, p_max_via_recursion_depth,
                        p_max_spring_over_recursion_depth)) {
            return false;
        }
    }

    // 5. 插入新走线
    int trace_half_width = board.rules.get_default_net_class().get_trace_half_width(p_layer);
    PolylineTrace new_trace = board.insert_trace(p_trace_shape, p_layer, trace_half_width,
                                                p_net_no_arr, p_cl_type, FixedState.UNFIXED);

    return new_trace != null;
}
```

## 7. 后处理优化

### 7.1 Pull-tight优化

```java
// PullTightAlgo45.java
@Override
Polyline pull_tight(Polyline p_polyline)
{
    Polyline new_result = avoid_acid_traps(p_polyline);  // 避免酸蚀陷阱
    Polyline prev_result = null;

    // 迭代优化直到收敛
    while (new_result != prev_result && !this.is_stop_requested()) {
        prev_result = new_result;
        Polyline tmp1 = reduce_corners(prev_result);      // 减少角点
        Polyline tmp2 = smoothen_corners(tmp1);           // 平滑角点
        new_result = reposition_lines(tmp2);              // 重新定位线段
    }
    return new_result;
}

private Polyline reduce_corners(Polyline p_polyline)
{
    if (p_polyline.arr.length <= 4) {
        return p_polyline;
    }

    // 尝试移除不必要的角点
    boolean corner_removed = false;
    List<Point> new_corners = new ArrayList<>();
    new_corners.add(p_polyline.corner(0)); // 起点

    for (int i = 1; i < p_polyline.arr.length - 2; ++i) {
        Point prev_corner = p_polyline.corner(i - 1);
        Point curr_corner = p_polyline.corner(i);
        Point next_corner = p_polyline.corner(i + 1);

        // 检查是否可以直接连接prev_corner和next_corner
        if (can_skip_corner(prev_corner, curr_corner, next_corner)) {
            corner_removed = true;
            continue; // 跳过当前角点
        }
        new_corners.add(curr_corner);
    }

    new_corners.add(p_polyline.corner(p_polyline.arr.length - 1)); // 终点

    if (corner_removed) {
        return new Polyline(new_corners.toArray(new Point[0]));
    }
    return p_polyline;
}
```

### 7.2 过孔优化

```java
// OptViaAlgo.java
public static boolean opt_via_location(RoutingBoard p_board, Via p_via,
                                     ExpansionCostFactor[] p_trace_cost_arr,
                                     int p_trace_pull_tight_accuracy,
                                     int p_max_recursion_depth)
{
    if (p_via.is_shove_fixed()) {
        return false; // 固定过孔不优化
    }

    // 只优化连接2条走线的过孔
    Collection<Item> contact_list = p_via.get_normal_contacts();
    if (contact_list.size() != 2) {
        return false;
    }

    // 获取连接的两条走线
    Iterator<Item> it = contact_list.iterator();
    Item first_contact = it.next();
    Item second_contact = it.next();

    if (!(first_contact instanceof PolylineTrace) || !(second_contact instanceof PolylineTrace)) {
        return false;
    }

    PolylineTrace first_trace = (PolylineTrace) first_contact;
    PolylineTrace second_trace = (PolylineTrace) second_contact;

    // 计算最优过孔位置
    Point optimal_via_location = calculate_optimal_via_location(first_trace, second_trace, p_via);

    if (optimal_via_location.equals(p_via.get_center())) {
        return false; // 位置已经最优
    }

    // 尝试移动过孔到最优位置
    boolean move_ok = p_board.move_drill_item(p_via, optimal_via_location, p_max_recursion_depth);

    if (move_ok) {
        // 优化连接的走线
        optimize_connected_traces(p_board, p_via, p_trace_cost_arr, p_trace_pull_tight_accuracy);
    }

    return move_ok;
}
```

## 8. 算法性能特点

### 8.1 时间复杂度分析

- **迷宫搜索**: O(n log n)，其中n是搜索空间大小
- **房间完成**: O(m)，其中m是障碍物数量
- **路径回溯**: O(k)，其中k是路径长度
- **走线插入**: O(p)，其中p是推挤对象数量

### 8.2 空间复杂度分析

- **扩展房间**: O(r)，其中r是房间数量
- **优先队列**: O(d)，其中d是门的数量
- **回溯数组**: O(h)，其中h是路径深度

### 8.3 优化策略

1. **启发式搜索**: 使用A*算法加速目标搜索
2. **分层处理**: 按层分别处理减少复杂度
3. **增量更新**: 只重新计算变化区域
4. **缓存机制**: 重用房间和门的计算结果
5. **并行处理**: 多线程处理独立的网络

## 9. 总结

Freerouting的布线算法采用了先进的房间-门模型和迷宫搜索技术，具有以下特点：

1. **几何精确性**: 基于精确几何计算，避免网格近似误差
2. **智能搜索**: 使用启发式搜索快速找到最优路径
3. **冲突处理**: 集成推挤和撕裂算法处理布线冲突
4. **多层支持**: 完整的多层PCB布线支持
5. **质量保证**: 严格遵循设计规则和制造约束

这种设计使得freerouting能够处理现代复杂PCB设计的各种挑战，为PCB设计自动化提供了强有力的技术支撑。
```
```
