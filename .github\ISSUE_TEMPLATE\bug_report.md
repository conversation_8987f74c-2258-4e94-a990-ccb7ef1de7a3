---
name: Bug report
about: Create a report to help us improve
title: ''
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment**
 - OS: [e.g. Windows 11, 64-bit]
 - Freerouting version: [e.g. 1.9]
 - EDA: [e.g. N/A, KiCad, EasyEDA]

**Command line arguments**
If applicable, include the full command line that you used.

**Project files**
If applicable, include project files like .pro, .kicad_pcb, .dsn.

**Logs**
Include at least the most recent lines of your log files or console output.

**Additional context**
Add any other context about the problem here.
